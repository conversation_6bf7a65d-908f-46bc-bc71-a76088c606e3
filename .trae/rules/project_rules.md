DO NOT <PERSON>KE MOCKUP, FAKE DATA, DUMMY DATA, JUST USE REAL DATA.
DO NOT USE ANY SOURCE IF AS TIKTOK, TWITTER OR X, FACEBOOK , INSTAGRAM, TWITTER, YOUTUBE just use from original source.
Always use best practice method already prove implemented for code.
Backend Using venv
Frontend Using pnpm 
log mean information display in terminal.
for large file or have big line code please chunk wise and following naming format.
for testing use real account tiktok use username "grafi<PERSON>e" password "<PERSON><PERSON><PERSON>@102410"
do not make other version file, just use single file approach not using old,new,simple,enchance.
follow structure already made
follow naming format like "Tiktok" tiktok_engine,tiktok_auth and etc.
do not make many file testing, before create check forlder "test" if not exist create it.
do not make long summary make slow chat window.
for doc please save on "docs" folder and always double check if exist or not.