'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
    Clock, CheckCircle, XCircle, AlertCircle, RefreshCw, Play, Pause, Square,
    Activity, BarChart3, Database, Calendar, User, Settings, Eye,
    FileText, TrendingUp, Target, Zap, Timer, Info, AlertTriangle
} from 'lucide-react';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import {
    getActorTaskDetails,
    executeActorTask,
    getActorScrapedData,
    getActorTaskLogs,
    updateActorTask,
    type ActorTask
} from '@/lib/api/actor-system';

interface TaskDetailModalProps {
    taskId: number | null;
    isOpen: boolean;
    onClose: () => void;
    onTaskUpdate?: () => void;
}

interface TaskLog {
    timestamp: string;
    level: 'info' | 'warning' | 'error' | 'success';
    message: string;
    details?: any;
}

interface TaskProgress {
    current: number;
    total: number;
    percentage: number;
    stage: string;
    estimatedTimeRemaining?: number;
}

export default function TaskDetailModal({ taskId, isOpen, onClose, onTaskUpdate }: TaskDetailModalProps) {
    const [task, setTask] = useState<ActorTask | null>(null);
    const [loading, setLoading] = useState(false);
    const [executing, setExecuting] = useState(false);
    const [pausing, setPausing] = useState(false);
    const [stopping, setStopping] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState('overview');
    const [logs, setLogs] = useState<TaskLog[]>([]);
    const [progress, setProgress] = useState<TaskProgress | null>(null);
    const [scrapedData, setScrapedData] = useState<any[]>([]);
    const [isPolling, setIsPolling] = useState(false);
    const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

    // Load task details
    const loadTaskDetails = async () => {
        if (!taskId) return;

        try {
            setLoading(true);
            setError(null);

            const taskDetails = await getActorTaskDetails(taskId);
            setTask(taskDetails);

            // Load real logs from backend
            await loadTaskLogs();

            // Load scraped data if task is completed
            if (taskDetails.status === 'COMPLETED') {
                await loadScrapedData();
            }

        } catch (err: any) {
            setError(err.message || 'Failed to load task details');
            addLog('error', 'Failed to load task details', err.message);
        } finally {
            setLoading(false);
        }
    };

    // Load task logs from backend
    const loadTaskLogs = async () => {
        if (!taskId) return;

        try {
            const logsResponse = await getActorTaskLogs(taskId);

            if (logsResponse.success && logsResponse.results) {
                // Convert backend logs to frontend format
                const backendLogs = logsResponse.results.map((log: any) => ({
                    timestamp: log.timestamp,
                    level: log.level as TaskLog['level'],
                    message: log.message,
                    details: log.details
                }));

                // Set logs (newest first)
                setLogs(backendLogs);

                if (backendLogs.length === 0) {
                    addLog('info', 'No execution logs available for this task');
                }
            } else {
                addLog('info', 'No execution logs available for this task');
            }
        } catch (err: any) {
            console.warn('Failed to load task logs:', err);
            addLog('info', 'Task details loaded - logs will appear during execution');
        }
    };

    // Load scraped data
    const loadScrapedData = async () => {
        if (!taskId) return;

        try {
            // Ensure we only get data for this specific task
            const data = await getActorScrapedData({
                task_id: taskId,
                limit: 100 // Increased limit to show more data
            });

            const scrapedItems = data.results || [];

            // Double-check filtering on frontend to ensure only this task's data
            const filteredItems = scrapedItems.filter(item =>
                item.task_id === taskId || item.task === taskId
            );

            setScrapedData(filteredItems);
            addLog('info', `Loaded ${filteredItems.length} scraped items for task ${taskId}`);

            if (filteredItems.length !== scrapedItems.length) {
                addLog('warning', `Filtered out ${scrapedItems.length - filteredItems.length} items from other tasks`);
            }
        } catch (err: any) {
            addLog('warning', 'Failed to load scraped data', err.message);
            setScrapedData([]);
        }
    };

    // Add log entry
    const addLog = (level: TaskLog['level'], message: string, details?: any) => {
        const newLog: TaskLog = {
            timestamp: new Date().toISOString(),
            level,
            message,
            details
        };
        setLogs(prev => [newLog, ...prev].slice(0, 100)); // Keep last 100 logs
    };

    // Execute task with authentication handling
    const handleExecuteTask = async () => {
        if (!taskId) return;

        try {
            setExecuting(true);
            setError(null);
            addLog('info', 'Starting task execution...');
            console.log('Executing task with ID:', taskId);

            const response = await executeActorTask(taskId);

            if (response.success) {
                setSuccess('Task execution started successfully!');
                addLog('success', 'Task execution started', response.message);
                
                // Start polling for progress
                startProgressPolling();
                
                // Switch to progress tab
                setActiveTab('progress');
                
                // Update task status
                if (task) {
                    setTask({ ...task, status: 'RUNNING', started_at: new Date().toISOString() });
                }
            } else {
                // Handle authentication errors specifically
                if (response.requires_reauth) {
                    const platform = response.platform || task?.platform || 'platform';
                    setError(
                        `🔐 Authentication Required: Your ${platform} session has expired. ` +
                        `Please go to the Accounts page and re-authenticate your account to continue scraping real data from ${platform}.`
                    );
                    addLog('warning', 'Authentication required', 
                        `Session expired for ${platform} account. Re-authentication needed.`);
                } else if (response.error?.includes('Session invalid') || response.error?.includes('re-authenticate')) {
                    setError(
                        `🔐 Session Invalid: Please re-authenticate your account first. ` +
                        `Go to Accounts → Select your account → Click "Re-authenticate" to login and enable real data scraping.`
                    );
                    addLog('warning', 'Session validation failed', response.error);
                } else {
                    setError(response.error || 'Failed to execute task');
                    addLog('error', 'Task execution failed', response.error);
                }
            }
        } catch (err: any) {
            setError(err.message || 'Failed to execute task');
            addLog('error', 'Task execution error', err.message);
        } finally {
            setExecuting(false);
        }
    };

    // Start progress polling
    const startProgressPolling = () => {
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }

        setIsPolling(true);
        addLog('info', 'Started real-time progress monitoring');

        const interval = setInterval(async () => {
            try {
                if (!taskId) return;

                // Get real task progress from backend
                const updatedTask = await getActorTaskDetails(taskId);

                // Update task state
                setTask(updatedTask);

                // Map task progress to progress display
                const taskProgress = updatedTask.progress || 0;
                const itemsScraped = updatedTask.items_scraped || 0;

                // Determine stage based on progress and status
                let stage = 'Initializing...';
                if (updatedTask.status === 'RUNNING') {
                    if (taskProgress < 20) {
                        stage = 'Setting up scraper...';
                    } else if (taskProgress < 30) {
                        stage = 'Starting scraping...';
                    } else if (taskProgress < 70) {
                        stage = 'Scraping content...';
                    } else if (taskProgress < 95) {
                        stage = 'Processing and saving data...';
                    } else {
                        stage = 'Finalizing...';
                    }
                } else if (updatedTask.status === 'COMPLETED') {
                    stage = `Completed - scraped ${itemsScraped} items`;
                } else if (updatedTask.status === 'FAILED') {
                    stage = `Failed: ${updatedTask.error_message || 'Unknown error'}`;
                }

                setProgress({
                    current: taskProgress,
                    total: 100,
                    percentage: taskProgress,
                    stage: stage,
                    estimatedTimeRemaining: updatedTask.status === 'RUNNING' ?
                        Math.max(0, (100 - taskProgress) * 1.5) : 0 // 1.5 seconds per percent
                });

                // Refresh logs to get real-time updates from Celery
                await loadTaskLogs();

                // Stop polling if task is completed or failed
                if (updatedTask.status === 'COMPLETED') {
                    stopProgressPolling();
                    await loadScrapedData();
                    await loadTaskLogs(); // Final log refresh
                } else if (updatedTask.status === 'FAILED') {
                    stopProgressPolling();
                    await loadTaskLogs(); // Final log refresh
                }
            } catch (err: any) {
                addLog('warning', 'Progress polling error', err.message);
            }
        }, 2000); // Poll every 2 seconds

        setPollingInterval(interval);
    };

    // Stop progress polling
    const handlePauseTask = async () => {
        if (!task) return;
        setPausing(true);
        try {
            const updatedTask = await updateActorTask(task.id, { status: 'PAUSED' });
            setTask(updatedTask);
            toast.success('Task paused successfully');
            stopProgressPolling();
        } catch (error) {
            console.error('Error pausing task:', error);
            toast.error('Failed to pause task');
        } finally {
            setPausing(false);
        }
    };

    const handleStopTask = async () => {
        if (!task) return;
        setStopping(true);
        try {
            const updatedTask = await updateActorTask(task.id, { status: 'CANCELLED' });
            setTask(updatedTask);
            toast.success('Task stopped successfully');
            stopProgressPolling();
        } catch (error) {
            console.error('Error stopping task:', error);
            toast.error('Failed to stop task');
        } finally {
            setStopping(false);
        }
    };

    const stopProgressPolling = () => {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
        }
        setIsPolling(false);
        addLog('info', 'Stopped progress monitoring');
    };

    // Get status icon
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'PENDING': return <Clock className="w-4 h-4 text-yellow-600" />;
            case 'RUNNING': return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
            case 'COMPLETED': return <CheckCircle className="w-4 h-4 text-green-600" />;
            case 'FAILED': return <XCircle className="w-4 h-4 text-red-600" />;
            case 'CANCELLED': return <Square className="w-4 h-4 text-gray-600" />;
            default: return <AlertCircle className="w-4 h-4 text-gray-600" />;
        }
    };

    // Get status color
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'PENDING': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'RUNNING': return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'COMPLETED': return 'bg-green-100 text-green-800 border-green-200';
            case 'FAILED': return 'bg-red-100 text-red-800 border-red-200';
            case 'CANCELLED': return 'bg-gray-100 text-gray-800 border-gray-200';
            default: return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    // Get log icon
    const getLogIcon = (level: TaskLog['level']) => {
        switch (level) {
            case 'info': return <Info className="w-4 h-4 text-blue-600" />;
            case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
            case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
            case 'error': return <XCircle className="w-4 h-4 text-red-600" />;
        }
    };

    // Format duration
    const formatDuration = (seconds: number) => {
        if (seconds < 60) return `${Math.round(seconds)}s`;
        if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
        return `${Math.round(seconds / 3600)}h`;
    };

    // Calculate task duration
    const getTaskDuration = () => {
        if (!task?.started_at) return null;
        const start = new Date(task.started_at);
        const end = task.completed_at ? new Date(task.completed_at) : new Date();
        return Math.floor((end.getTime() - start.getTime()) / 1000);
    };

    useEffect(() => {
        if (isOpen && taskId) {
            loadTaskDetails();
        }
        
        // Don't stop polling when modal closes - let tasks continue in background
        // Only cleanup when component unmounts or taskId changes
        return () => {
            // Only clear polling if taskId is changing or component is unmounting
            // Don't clear when just closing the modal (isOpen changes)
        };
    }, [taskId]); // Removed isOpen dependency

    // Separate effect to handle modal close without stopping polling
    useEffect(() => {
        // When modal closes, keep polling running for background tasks
        if (!isOpen && pollingInterval && task?.status === 'RUNNING') {
            // Keep polling in background for running tasks
            console.log('Modal closed but keeping task polling active for background execution');
        }
    }, [isOpen, pollingInterval, task?.status]);

    // Cleanup polling only when component unmounts or task changes
    useEffect(() => {
        return () => {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                setPollingInterval(null);
            }
        };
    }, [taskId]); // Only cleanup when taskId changes

    useEffect(() => {
        if (success) {
            const timer = setTimeout(() => setSuccess(null), 5000);
            return () => clearTimeout(timer);
        }
    }, [success]);

    if (!isOpen || !taskId) return null;

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Activity className="w-5 h-5" />
                        Task Details - {task?.name || `Task ${taskId}`}
                        {task && (
                            <Badge className={getStatusColor(task.status)}>
                                {getStatusIcon(task.status)}
                                <span className="ml-1">{task.status}</span>
                            </Badge>
                        )}
                    </DialogTitle>
                </DialogHeader>

                {error && (
                    <Alert className="border-red-200 bg-red-50">
                        <XCircle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">{error}</AlertDescription>
                    </Alert>
                )}

                {success && (
                    <Alert className="border-green-200 bg-green-50">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">{success}</AlertDescription>
                    </Alert>
                )}

                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
                    <TabsList className="grid w-full grid-cols-5">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="progress">Progress</TabsTrigger>
                        <TabsTrigger value="logs">Logs</TabsTrigger>
                        <TabsTrigger value="data">Data</TabsTrigger>
                        <TabsTrigger value="settings">Settings</TabsTrigger>
                    </TabsList>

                    <div className="mt-4 h-[60vh] overflow-hidden">
                        <TabsContent value="overview" className="h-full">
                            <ScrollArea className="h-full">
                                <div className="space-y-4">
                                    {loading ? (
                                        <div className="space-y-4">
                                            <Skeleton className="h-32 w-full" />
                                            <Skeleton className="h-24 w-full" />
                                            <Skeleton className="h-24 w-full" />
                                        </div>
                                    ) : task ? (
                                        <>
                                            {/* Task Summary */}
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Target className="w-5 h-5" />
                                                        Task Summary
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="grid grid-cols-2 gap-4">
                                                        <div>
                                                            <p className="text-sm text-gray-600">Task Name</p>
                                                            <p className="font-medium">{task.name}</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Task Type</p>
                                                            <p className="font-medium">{task.type}</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Platform</p>
                                                            <Badge variant="outline">{task.platform}</Badge>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Status</p>
                                                            <Badge className={getStatusColor(task.status)}>
                                                                {getStatusIcon(task.status)}
                                                                <span className="ml-1">{task.status}</span>
                                                            </Badge>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Keywords</p>
                                                            <p className="font-medium">{task.keywords || 'N/A'}</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Max Items</p>
                                                            <p className="font-medium">{task.max_items}</p>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>

                                            {/* Task Timeline */}
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Calendar className="w-5 h-5" />
                                                        Timeline
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="space-y-3">
                                                        <div className="flex items-center gap-3">
                                                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                                            <div>
                                                                <p className="text-sm font-medium">Created</p>
                                                                <p className="text-xs text-gray-600">
                                                                    {new Date(task.created_at).toLocaleString()}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        {task.started_at && (
                                                            <div className="flex items-center gap-3">
                                                                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                                                                <div>
                                                                    <p className="text-sm font-medium">Started</p>
                                                                    <p className="text-xs text-gray-600">
                                                                        {new Date(task.started_at).toLocaleString()}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        )}
                                                        {task.completed_at && (
                                                            <div className="flex items-center gap-3">
                                                                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                                                                <div>
                                                                    <p className="text-sm font-medium">Completed</p>
                                                                    <p className="text-xs text-gray-600">
                                                                        {new Date(task.completed_at).toLocaleString()}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        )}
                                                        {getTaskDuration() && (
                                                            <div className="mt-2 p-2 bg-gray-50 rounded">
                                                                <p className="text-sm">
                                                                    <strong>Duration:</strong> {formatDuration(getTaskDuration()!)}
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                </CardContent>
                                            </Card>

                                            {/* Task Actions */}
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <Settings className="w-5 h-5" />
                                                        Actions
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="flex gap-2">
                                                        {task.status === 'PENDING' && (
                                                            <Button 
                                                                onClick={handleExecuteTask} 
                                                                disabled={executing}
                                                                className="flex-1"
                                                            >
                                                                {executing ? (
                                                                    <>
                                                                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                                                        Executing...
                                                                    </>
                                                                ) : (
                                                                    <>
                                                                        <Play className="w-4 h-4 mr-2" />
                                                                        Execute Task
                                                                    </>
                                                                )}
                                                            </Button>
                                                        )}
                                                        {task.status === 'RUNNING' && (
                                                            <>
                                                            <Button 
                                                                variant="outline" 
                                                                onClick={handlePauseTask}
                                                                disabled={pausing}
                                                                className="flex-1"
                                                            >
                                                                <Pause className="w-4 h-4 mr-2" />
                                                                Pause
                                                            </Button>
                                                            <Button 
                                                                variant="destructive" 
                                                                onClick={handleStopTask}
                                                                disabled={stopping}
                                                                className="flex-1"
                                                            >
                                                                <Square className="w-4 h-4 mr-2" />
                                                                Stop
                                                            </Button>
                                                            </>
                                                        )}
                                                        {task.status === 'PAUSED' && (
                                                            <Button 
                                                                onClick={handleExecuteTask} 
                                                                disabled={executing}
                                                                className="flex-1"
                                                            >
                                                                <Play className="w-4 h-4 mr-2" />
                                                                Resume
                                                            </Button>
                                                        )}
                                                        <Button 
                                                            variant="outline" 
                                                            onClick={loadTaskDetails}
                                                            disabled={loading}
                                                        >
                                                            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                                                            Refresh
                                                        </Button>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </>
                                    ) : (
                                        <div className="text-center py-8">
                                            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-600">Task not found</p>
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        </TabsContent>

                        <TabsContent value="progress" className="h-full">
                            <ScrollArea className="h-full">
                                <div className="space-y-4">
                                    {task?.status === 'RUNNING' || progress ? (
                                        <>
                                            {/* Progress Overview */}
                                            <Card>
                                                <CardHeader>
                                                    <CardTitle className="flex items-center gap-2">
                                                        <BarChart3 className="w-5 h-5" />
                                                        Real-time Progress
                                                        {isPolling && (
                                                            <div className="flex items-center gap-1 text-sm text-green-600">
                                                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                                                Live
                                                            </div>
                                                        )}
                                                    </CardTitle>
                                                </CardHeader>
                                                <CardContent>
                                                    {progress ? (
                                                        <div className="space-y-4">
                                                            <div>
                                                                <div className="flex justify-between items-center mb-2">
                                                                    <span className="text-sm font-medium">{progress.stage}</span>
                                                                    <span className="text-sm text-gray-600">
                                                                        {Math.round(progress.percentage)}%
                                                                    </span>
                                                                </div>
                                                                <Progress value={progress.percentage} className="h-3" />
                                                                {task?.items_scraped && task.items_scraped > 0 && (
                                                                    <p className="text-xs text-gray-500 mt-1">
                                                                        {task.items_scraped} items scraped so far
                                                                    </p>
                                                                )}
                                                            </div>
                                                            <div className="grid grid-cols-3 gap-4 text-sm">
                                                                <div>
                                                                    <p className="text-gray-600">Items Processed</p>
                                                                    <p className="font-medium">
                                                                        {Math.round(progress.current)} / {progress.total}
                                                                    </p>
                                                                </div>
                                                                <div>
                                                                    <p className="text-gray-600">Completion</p>
                                                                    <p className="font-medium">{Math.round(progress.percentage)}%</p>
                                                                </div>
                                                                <div>
                                                                    <p className="text-gray-600">Est. Time Remaining</p>
                                                                    <p className="font-medium">
                                                                        {progress.estimatedTimeRemaining ? 
                                                                            formatDuration(progress.estimatedTimeRemaining) : 'N/A'
                                                                        }
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div className="text-center py-8">
                                                            <Timer className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                                            <p className="text-gray-600">No progress data available</p>
                                                        </div>
                                                    )}
                                                </CardContent>
                                            </Card>

                                            {/* Real-time Stats */}
                                            {task && (
                                                <Card>
                                                    <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                            <TrendingUp className="w-5 h-5" />
                                                            Real-time Stats
                                                            {isPolling && (
                                                                <div className="flex items-center gap-1 text-sm text-green-600">
                                                                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                                                    Live
                                                                </div>
                                                            )}
                                                        </CardTitle>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <div className="grid grid-cols-2 gap-4">
                                                            <div className="p-4 bg-blue-50 rounded-lg">
                                                                <p className="text-sm text-blue-600 mb-1">Items Scraped</p>
                                                                <p className="text-3xl font-bold text-blue-900">
                                                                    {task.items_scraped ?? 0}
                                                                </p>
                                                                <p className="text-xs text-blue-600 mt-1">
                                                                    {task.platform} {task.task_type}
                                                                </p>
                                                            </div>
                                                            <div className="p-4 bg-green-50 rounded-lg">
                                                                <p className="text-sm text-green-600 mb-1">Task Progress</p>
                                                                <p className="text-3xl font-bold text-green-900">
                                                                    {Math.round(task.progress ?? 0)}%
                                                                </p>
                                                                <p className="text-xs text-green-600 mt-1">
                                                                    Status: {task.status}
                                                                </p>
                                                            </div>
                                                        </div>

                                                        {/* Task Timeline */}
                                                        <div className="mt-4 pt-4 border-t">
                                                            <h4 className="text-sm font-medium mb-3">Task Timeline</h4>
                                                            <div className="space-y-2 text-sm">
                                                                {task.created_at && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">Created:</span>
                                                                        <span>{new Date(task.created_at).toLocaleString()}</span>
                                                                    </div>
                                                                )}
                                                                {task.started_at && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">Started:</span>
                                                                        <span>{new Date(task.started_at).toLocaleString()}</span>
                                                                    </div>
                                                                )}
                                                                {task.completed_at && (
                                                                    <div className="flex justify-between">
                                                                        <span className="text-gray-600">Completed:</span>
                                                                        <span>{new Date(task.completed_at).toLocaleString()}</span>
                                                                    </div>
                                                                )}
                                                                {task.started_at && task.completed_at && (
                                                                    <div className="flex justify-between font-medium">
                                                                        <span className="text-gray-600">Duration:</span>
                                                                        <span>
                                                                            {Math.round((new Date(task.completed_at).getTime() - new Date(task.started_at).getTime()) / 1000)}s
                                                                        </span>
                                                                    </div>
                                                                )}
                                                                {task.error_message && (
                                                                    <div className="flex justify-between text-red-600">
                                                                        <span>Error:</span>
                                                                        <span className="text-right max-w-xs truncate">{task.error_message}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            )}
                                        </>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-600">Task not running</p>
                                            <p className="text-sm text-gray-500 mt-2">
                                                Execute the task to see real-time progress
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        </TabsContent>

                        <TabsContent value="logs" className="h-full">
                            <ScrollArea className="h-full">
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="font-medium flex items-center gap-2">
                                            <FileText className="w-4 h-4" />
                                            Execution Logs ({logs.length})
                                        </h3>
                                        <Button 
                                            variant="outline" 
                                            size="sm" 
                                            onClick={() => setLogs([])}
                                        >
                                            Clear Logs
                                        </Button>
                                    </div>
                                    {logs.length > 0 ? (
                                        logs.map((log, index) => (
                                            <div 
                                                key={index} 
                                                className="p-3 border rounded-md bg-white hover:bg-gray-50"
                                            >
                                                <div className="flex items-start gap-2">
                                                    {getLogIcon(log.level)}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center gap-2 mb-1">
                                                            <span className="text-sm font-medium">{log.message}</span>
                                                            <span className="text-xs text-gray-500">
                                                                {new Date(log.timestamp).toLocaleTimeString()}
                                                            </span>
                                                        </div>
                                                        {log.details && (
                                                            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded overflow-x-auto">
                                                                {typeof log.details === 'string' ? 
                                                                    log.details : 
                                                                    JSON.stringify(log.details, null, 2)
                                                                }
                                                            </pre>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <div className="text-center py-8">
                                            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-600">No logs available</p>
                                            <p className="text-sm text-gray-500 mt-2">
                                                Logs will appear here during task execution
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        </TabsContent>

                        <TabsContent value="data" className="h-full">
                            <ScrollArea className="h-full">
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <h3 className="font-medium flex items-center gap-2">
                                            <Database className="w-4 h-4" />
                                            Scraped Data ({scrapedData.length})
                                        </h3>
                                        <Button 
                                            variant="outline" 
                                            size="sm" 
                                            onClick={loadScrapedData}
                                        >
                                            <RefreshCw className="w-4 h-4 mr-2" />
                                            Refresh
                                        </Button>
                                    </div>
                                    {scrapedData.length > 0 ? (
                                        <div className="space-y-3">
                                            {scrapedData.map((item, index) => (
                                                <Card key={index}>
                                                    <CardContent className="p-4">
                                                        <div className="flex items-start gap-3">
                                                            <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                                                            <div className="flex-1">
                                                                <div className="flex items-center gap-2 mb-2">
                                                                    <Badge variant="outline">{item.data_type}</Badge>
                                                                    <span className="text-xs text-gray-500">
                                                                        {new Date(item.scraped_at).toLocaleString()}
                                                                    </span>
                                                                </div>
                                                                <div className="text-sm">
                                                                    <p className="font-medium mb-1">
                                                                        {item.content?.title || item.content?.text || 'Untitled'}
                                                                    </p>
                                                                    {item.content?.author && (
                                                                        <p className="text-gray-600">
                                                                            By: {item.content.author}
                                                                        </p>
                                                                    )}
                                                                    {item.content?.stats && (
                                                                        <div className="flex gap-4 mt-2 text-xs text-gray-500">
                                                                            <span>👍 {item.content.stats.likes || 0}</span>
                                                                            <span>💬 {item.content.stats.comments || 0}</span>
                                                                            <span>👁️ {item.content.stats.views || 0}</span>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-600">No data scraped yet</p>
                                            <p className="text-sm text-gray-500 mt-2">
                                                Data will appear here after task execution
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        </TabsContent>

                        <TabsContent value="settings" className="h-full">
                            <ScrollArea className="h-full">
                                <div className="space-y-4">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <Settings className="w-5 h-5" />
                                                Task Configuration
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            {task ? (
                                                <div className="space-y-4">
                                                    <div>
                                                        <p className="text-sm font-medium mb-2">Task Parameters</p>
                                                        <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                                                            {JSON.stringify(task.task_parameters, null, 2)}
                                                        </pre>
                                                    </div>
                                                    <div className="grid grid-cols-2 gap-4">
                                                        <div>
                                                            <p className="text-sm text-gray-600">Max Items</p>
                                                            <p className="font-medium">{task.max_items}</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Platform</p>
                                                            <p className="font-medium">{task.platform}</p>
                                                        </div>
                                                    </div>
                                                    {task.error_message && (
                                                        <div>
                                                            <p className="text-sm font-medium mb-2 text-red-600">Error Message</p>
                                                            <div className="p-3 bg-red-50 border border-red-200 rounded">
                                                                <p className="text-sm text-red-800">{task.error_message}</p>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            ) : (
                                                <div className="text-center py-8">
                                                    <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                                    <p className="text-gray-600">No configuration available</p>
                                                </div>
                                            )}
                                        </CardContent>
                                    </Card>
                                </div>
                            </ScrollArea>
                        </TabsContent>
                    </div>
                </Tabs>
            </DialogContent>
        </Dialog>
    );
}