'use client';

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
    Plus, Edit, Trash2, Play, Pause, Square, RefreshCw, Search, Filter, 
    CheckCircle, AlertCircle, Clock, Target, Calendar, User, Activity
} from "lucide-react";
import { 
    createActorTask, 
    executeActorTask,
    getActorAccounts,
    getPlatformIcon,
    getPlatformColor,
    getTaskType<PERSON>abel,
    getStatusColor,
    type ActorAccount,
    type ActorTask,
    type CreateTaskData
} from "@/lib/api/actor-system";

interface EnhancedTaskListProps {
    tasks: ActorTask[];
    onTasksChange: () => void;
}

interface TaskFormData {
    account_id: number;
    task_type: string;
    task_name: string;
    target_identifier: string;
    keywords: string;
    max_items: number;
    start_date: string;
    end_date: string;
}

export function EnhancedTaskList({ tasks, onTasksChange }: EnhancedTaskListProps) {
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [editingTask, setEditingTask] = useState<ActorTask | null>(null);
    const [formData, setFormData] = useState<TaskFormData>({
        account_id: 0,
        task_type: 'CONTENT_SEARCH',
        task_name: '',
        target_identifier: '',
        keywords: '',
        max_items: 50,
        start_date: '',
        end_date: ''
    });
    const [loading, setLoading] = useState(false);
    const [executing, setExecuting] = useState<number | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('ALL');
    const [platformFilter, setPlatformFilter] = useState('ALL');

    useEffect(() => {
        loadAccounts();
    }, []);

    const loadAccounts = async () => {
        try {
            const userAccounts = await getActorAccounts();
            setAccounts(userAccounts);
            
            // Set default account if available
            if (userAccounts.length > 0 && formData.account_id === 0) {
                setFormData(prev => ({ ...prev, account_id: userAccounts[0].id }));
            }
        } catch (err: any) {
            console.error('Error loading accounts:', err);
            setError('Failed to load accounts');
        }
    };

    const resetForm = () => {
        setFormData({
            account_id: accounts.length > 0 ? accounts[0].id : 0,
            task_type: 'CONTENT_SEARCH',
            task_name: '',
            target_identifier: '',
            keywords: '',
            max_items: 50,
            start_date: '',
            end_date: ''
        });
        setError(null);
        setSuccess(null);
    };

    const handleCreate = async () => {
        if (!formData.account_id || !formData.task_name) {
            setError('Account and task name are required');
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const taskData: CreateTaskData = {
                account_id: formData.account_id,
                task_type: formData.task_type,
                task_name: formData.task_name,
                target_identifier: formData.target_identifier || undefined,
                keywords: formData.keywords || undefined,
                max_items: formData.max_items,
                start_date: formData.start_date || undefined,
                end_date: formData.end_date || undefined
            };

            const result = await createActorTask(taskData);

            if (result.success) {
                setSuccess('Task created successfully');
                setShowCreateForm(false);
                resetForm();
                onTasksChange();
            } else {
                setError(result.error || 'Failed to create task');
            }
        } catch (error: any) {
            console.error('Error creating task:', error);
            setError(error.response?.data?.error || 'Failed to create task');
        } finally {
            setLoading(false);
        }
    };

    const handleExecute = async (taskId: number) => {
        try {
            setExecuting(taskId);
            setError(null);

            const result = await executeActorTask(taskId);

            if (result.success) {
                setSuccess(`Task executed successfully. Scraped ${result.items_scraped} items.`);
                onTasksChange();
            } else {
                setError(result.error || 'Failed to execute task');
            }
        } catch (error: any) {
            console.error('Error executing task:', error);
            setError(error.response?.data?.error || 'Failed to execute task');
        } finally {
            setExecuting(null);
        }
    };

    const handleDelete = async (taskId: number) => {
        if (!confirm('Are you sure you want to delete this task?')) {
            return;
        }

        try {
            setLoading(true);
            // TODO: Implement delete task API
            setSuccess('Task deleted successfully');
            onTasksChange();
        } catch (error: any) {
            console.error('Error deleting task:', error);
            setError('Failed to delete task');
        } finally {
            setLoading(false);
        }
    };

    const getFilteredTasks = () => {
        return tasks.filter(task => {
            const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                task.type.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'ALL' || task.status === statusFilter;
            const matchesPlatform = platformFilter === 'ALL' || task.platform === platformFilter;
            
            return matchesSearch && matchesStatus && matchesPlatform;
        });
    };

    const getTaskStats = () => {
        const total = tasks.length;
        const pending = tasks.filter(t => t.status === 'PENDING').length;
        const running = tasks.filter(t => t.status === 'RUNNING').length;
        const completed = tasks.filter(t => t.status === 'COMPLETED').length;
        const failed = tasks.filter(t => t.status === 'FAILED').length;
        const platforms = new Set(tasks.map(t => t.platform)).size;

        return { total, pending, running, completed, failed, platforms };
    };

    const stats = getTaskStats();
    const filteredTasks = getFilteredTasks();
    const selectedAccount = accounts.find(a => a.id === formData.account_id);

    return (
        <div className="space-y-6">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {success && (
                <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>{success}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Target className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Tasks</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Clock className="h-8 w-8 text-yellow-500" />
                        <div>
                            <p className="text-sm text-gray-500">Pending</p>
                            <p className="text-2xl font-bold">{stats.pending}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Play className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Running</p>
                            <p className="text-2xl font-bold">{stats.running}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <CheckCircle className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Completed</p>
                            <p className="text-2xl font-bold">{stats.completed}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <AlertCircle className="h-8 w-8 text-red-500" />
                        <div>
                            <p className="text-sm text-gray-500">Failed</p>
                            <p className="text-2xl font-bold">{stats.failed}</p>
                        </div>
                    </div>
                </Card>
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Activity className="h-8 w-8 text-purple-500" />
                        <div>
                            <p className="text-sm text-gray-500">Platforms</p>
                            <p className="text-2xl font-bold">{stats.platforms}</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Controls */}
            <div className="flex justify-between items-center">
                <div className="flex gap-4 items-center">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search tasks..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10 w-64"
                        />
                    </div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Status</SelectItem>
                            <SelectItem value="PENDING">Pending</SelectItem>
                            <SelectItem value="RUNNING">Running</SelectItem>
                            <SelectItem value="COMPLETED">Completed</SelectItem>
                            <SelectItem value="FAILED">Failed</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={platformFilter} onValueChange={setPlatformFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Platforms</SelectItem>
                            <SelectItem value="tiktok">TikTok</SelectItem>
                            <SelectItem value="twitter">Twitter</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex gap-2">
                    <Button onClick={onTasksChange} variant="outline" size="sm">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                    <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
                        <DialogTrigger asChild>
                            <Button size="sm">
                                <Plus className="h-4 w-4 mr-2" />
                                Create Task
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Create New Task</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                {/* Account Selection */}
                                <div>
                                    <Label htmlFor="account">Account *</Label>
                                    <Select
                                        value={formData.account_id.toString()}
                                        onValueChange={(value) => setFormData({...formData, account_id: parseInt(value)})}
                                    >
                                        <SelectTrigger>
                                            <SelectValue>
                                                {selectedAccount && (
                                                    <div className="flex items-center gap-2">
                                                        <span>{getPlatformIcon(selectedAccount.platform)}</span>
                                                        <span>@{selectedAccount.username}</span>
                                                        <Badge variant="outline">{selectedAccount.platform}</Badge>
                                                    </div>
                                                )}
                                            </SelectValue>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {accounts.map((account) => (
                                                <SelectItem key={account.id} value={account.id.toString()}>
                                                    <div className="flex items-center gap-2">
                                                        <span>{getPlatformIcon(account.platform)}</span>
                                                        <span>@{account.username}</span>
                                                        <Badge variant="outline">{account.platform}</Badge>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Task Type */}
                                <div>
                                    <Label htmlFor="task_type">Task Type *</Label>
                                    <Select
                                        value={formData.task_type}
                                        onValueChange={(value) => setFormData({...formData, task_type: value})}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="CONTENT_SEARCH">Content Search</SelectItem>
                                            <SelectItem value="MY_VIDEOS">My Content</SelectItem>
                                            <SelectItem value="TARGETED_USER">User Analysis</SelectItem>
                                            <SelectItem value="FEED_SCRAPE">Feed Scraping</SelectItem>
                                            <SelectItem value="HASHTAG_ANALYSIS">Hashtag Analysis</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Task Name */}
                                <div>
                                    <Label htmlFor="task_name">Task Name *</Label>
                                    <Input
                                        id="task_name"
                                        value={formData.task_name}
                                        onChange={(e) => setFormData({...formData, task_name: e.target.value})}
                                        placeholder="Enter task name"
                                    />
                                </div>

                                {/* Keywords */}
                                <div>
                                    <Label htmlFor="keywords">Keywords</Label>
                                    <Input
                                        id="keywords"
                                        value={formData.keywords}
                                        onChange={(e) => setFormData({...formData, keywords: e.target.value})}
                                        placeholder="Enter keywords (comma-separated)"
                                    />
                                </div>

                                {/* Max Items */}
                                <div>
                                    <Label htmlFor="max_items">Max Items</Label>
                                    <Input
                                        id="max_items"
                                        type="number"
                                        value={formData.max_items}
                                        onChange={(e) => setFormData({...formData, max_items: parseInt(e.target.value) || 50})}
                                        min="1"
                                        max="1000"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                                        Cancel
                                    </Button>
                                    <Button onClick={handleCreate} disabled={loading}>
                                        {loading ? 'Creating...' : 'Create Task'}
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {/* Task List */}
            {filteredTasks.length === 0 ? (
                <Card className="p-8 text-center">
                    <Target className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No tasks found</h3>
                    <p className="text-gray-600 mb-4">
                        {tasks.length === 0 
                            ? "Create your first automation task to get started."
                            : "No tasks match your current filters."
                        }
                    </p>
                    <Button onClick={() => setShowCreateForm(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Your First Task
                    </Button>
                </Card>
            ) : (
                <div className="grid gap-4">
                    {filteredTasks.map((task) => (
                        <Card key={task.id} className="p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {/* Platform Icon */}
                                    <div className={`p-3 rounded-full ${getPlatformColor(task.platform)}`}>
                                        <span className="text-lg">{getPlatformIcon(task.platform)}</span>
                                    </div>
                                    
                                    {/* Task Info */}
                                    <div>
                                        <div className="flex items-center gap-2 mb-1">
                                            <h4 className="font-medium">{task.name}</h4>
                                            <Badge className={getStatusColor(task.status)}>
                                                {task.status}
                                            </Badge>
                                            <Badge variant="outline">
                                                {getTaskTypeLabel(task.type)}
                                            </Badge>
                                            <Badge variant="outline">
                                                {task.platform.charAt(0).toUpperCase() + task.platform.slice(1)}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                            <span>Created: {new Date(task.created_at).toLocaleDateString()}</span>
                                            {task.keywords && <span>• Keywords: {task.keywords}</span>}
                                            {task.max_items && <span>• Max: {task.max_items} items</span>}
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center gap-2">
                                    {task.status === 'PENDING' && (
                                        <Button
                                            size="sm"
                                            onClick={() => handleExecute(task.id)}
                                            disabled={executing === task.id}
                                        >
                                            {executing === task.id ? (
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                            ) : (
                                                <Play className="h-4 w-4" />
                                            )}
                                        </Button>
                                    )}
                                    
                                    <Button size="sm" variant="outline">
                                        <Edit className="h-4 w-4" />
                                    </Button>

                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleDelete(task.id)}
                                        disabled={loading}
                                        className="text-red-600 hover:text-red-700"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
}
