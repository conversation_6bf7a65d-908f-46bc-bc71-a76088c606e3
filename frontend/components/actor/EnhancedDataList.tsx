'use client';

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
    Database, Download, Eye, Activity,
    AlertCircle, RefreshCw, BarChart3, Target
} from "lucide-react";
import {
    getActorScrapedData,
    getDataLabelingStats,
    getPlatformColor,
    type ActorAccount
} from "@/lib/api/actor-system";

interface ScrapedDataItem {
    id: number;
    data_type: string;
    platform: string;
    content: any;
    quality_score: number;
    scraped_at: string;
    account_info?: ActorAccount;
}

interface DataStats {
    total_items: number;
    platforms: Record<string, number>;
    data_types: Record<string, number>;
    quality_distribution: Record<string, number>;
}

interface EnhancedDataListProps {
    accounts: ActorAccount[];
}

export function EnhancedDataList({ accounts }: EnhancedDataListProps) {
    const [data, setData] = useState<ScrapedDataItem[]>([]);
    const [stats, setStats] = useState<DataStats | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [platformFilter, setPlatformFilter] = useState('ALL');
    const [dataTypeFilter, setDataTypeFilter] = useState('ALL');
    const [qualityFilter, setQualityFilter] = useState('ALL');
    const [accountFilter, setAccountFilter] = useState('ALL');

    const loadData = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await getActorScrapedData();
            setData(response.results || []);
            
            const statsResponse = await getDataLabelingStats();
            if (statsResponse.success && statsResponse.stats) {
                // Transform the API response to match the expected interface
                const transformedStats: DataStats = {
                    total_items: statsResponse.stats.total_items || 0,
                    platforms: statsResponse.stats.platform_breakdown || {},
                    data_types: statsResponse.stats.data_type_breakdown || {},
                    quality_distribution: {} // We can add this later if needed
                };
                setStats(transformedStats);
            } else {
                // Set default empty stats if API fails
                setStats({
                    total_items: 0,
                    platforms: {},
                    data_types: {},
                    quality_distribution: {}
                });
            }
        } catch (err: any) {
            setError(err.message || 'Failed to load data');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadData();
    }, []);

    const getFilteredData = () => {
        return data.filter(item => {
            const matchesSearch = searchTerm === '' || 
                JSON.stringify(item.content).toLowerCase().includes(searchTerm.toLowerCase());
            const matchesPlatform = platformFilter === 'ALL' || item.platform === platformFilter;
            const matchesDataType = dataTypeFilter === 'ALL' || item.data_type === dataTypeFilter;
            const matchesAccount = accountFilter === 'ALL' || item.account_info?.id.toString() === accountFilter;
            
            let matchesQuality = true;
            if (qualityFilter === 'HIGH') matchesQuality = item.quality_score >= 0.8;
            else if (qualityFilter === 'MEDIUM') matchesQuality = item.quality_score >= 0.5 && item.quality_score < 0.8;
            else if (qualityFilter === 'LOW') matchesQuality = item.quality_score < 0.5;

            return matchesSearch && matchesPlatform && matchesDataType && matchesAccount && matchesQuality;
        });
    };

    const getQualityColor = (score: number) => {
        if (score >= 0.8) return 'bg-green-100 text-green-800';
        if (score >= 0.5) return 'bg-yellow-100 text-yellow-800';
        return 'bg-red-100 text-red-800';
    };

    const getQualityLabel = (score: number) => {
        if (score >= 0.8) return 'High';
        if (score >= 0.5) return 'Medium';
        return 'Low';
    };

    const filteredData = getFilteredData();

    return (
        <div className="space-y-6">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            {stats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card className="p-4">
                        <div className="flex items-center gap-2">
                            <Database className="h-5 w-5 text-blue-600" />
                            <div>
                                <p className="text-sm text-gray-600">Total Items</p>
                                <p className="text-2xl font-bold">{stats?.total_items || 0}</p>
                            </div>
                        </div>
                    </Card>
                    
                    <Card className="p-4">
                        <div className="flex items-center gap-2">
                            <Activity className="h-5 w-5 text-green-600" />
                            <div>
                                <p className="text-sm text-gray-600">Platforms</p>
                                <p className="text-2xl font-bold">{stats?.platforms ? Object.keys(stats.platforms).length : 0}</p>
                            </div>
                        </div>
                    </Card>

                    <Card className="p-4">
                        <div className="flex items-center gap-2">
                            <Target className="h-5 w-5 text-purple-600" />
                            <div>
                                <p className="text-sm text-gray-600">Data Types</p>
                                <p className="text-2xl font-bold">{stats?.data_types ? Object.keys(stats.data_types).length : 0}</p>
                            </div>
                        </div>
                    </Card>
                    
                    <Card className="p-4">
                        <div className="flex items-center gap-2">
                            <BarChart3 className="h-5 w-5 text-orange-600" />
                            <div>
                                <p className="text-sm text-gray-600">Avg Quality</p>
                                <p className="text-2xl font-bold">
                                    {filteredData.length > 0 
                                        ? (filteredData.reduce((sum, item) => sum + item.quality_score, 0) / filteredData.length).toFixed(1)
                                        : '0.0'
                                    }
                                </p>
                            </div>
                        </div>
                    </Card>
                </div>
            )}

            {/* Filters */}
            <Card className="p-4">
                <div className="flex flex-wrap gap-4">
                    <div className="flex-1 min-w-64">
                        <Input
                            placeholder="Search data..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full"
                        />
                    </div>
                    
                    <Select value={platformFilter} onValueChange={setPlatformFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Platform" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Platforms</SelectItem>
                            <SelectItem value="tiktok">TikTok</SelectItem>
                            <SelectItem value="twitter">Twitter</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Select value={qualityFilter} onValueChange={setQualityFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue placeholder="Quality" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Quality</SelectItem>
                            <SelectItem value="HIGH">High (≥80%)</SelectItem>
                            <SelectItem value="MEDIUM">Medium (50-79%)</SelectItem>
                            <SelectItem value="LOW">Low (&lt;50%)</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Button onClick={loadData} variant="outline" disabled={loading}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                </div>
            </Card>

            {/* Data List */}
            {loading ? (
                <Card className="p-8 text-center">
                    <RefreshCw className="h-8 w-8 mx-auto animate-spin text-gray-400 mb-4" />
                    <p className="text-gray-600">Loading data...</p>
                </Card>
            ) : filteredData.length === 0 ? (
                <Card className="p-8 text-center">
                    <Database className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No data found</h3>
                    <p className="text-gray-600 mb-4">
                        {data.length === 0 
                            ? "No scraped data available yet."
                            : "No data matches your current filters."
                        }
                    </p>
                </Card>
            ) : (
                <div className="space-y-4">
                    {filteredData.map((item) => (
                        <Card key={item.id} className="p-4">
                            <div className="flex items-start justify-between">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <Badge className={getPlatformColor(item.platform)}>
                                            {item.platform}
                                        </Badge>
                                        <Badge variant="outline">
                                            {item.data_type}
                                        </Badge>
                                        <Badge className={getQualityColor(item.quality_score)}>
                                            {getQualityLabel(item.quality_score)} ({Math.round(item.quality_score * 100)}%)
                                        </Badge>
                                    </div>
                                    
                                    <div className="text-sm text-gray-600 mb-2">
                                        Scraped: {new Date(item.scraped_at).toLocaleString()}
                                    </div>
                                    
                                    <div className="bg-gray-50 p-3 rounded text-sm">
                                        <pre className="whitespace-pre-wrap">
                                            {JSON.stringify(item.content, null, 2)}
                                        </pre>
                                    </div>
                                </div>
                                
                                <div className="flex gap-2 ml-4">
                                    <Button variant="outline" size="sm">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                    <Button variant="outline" size="sm">
                                        <Download className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}

            <div className="text-center text-sm text-gray-500">
                Showing {filteredData.length} of {data.length} items
            </div>
        </div>
    );
}
