"use client"

import * as React from "react"
import {
  BookOpen,
  Edit3,
  Home,
  Settings2,
  User,
  Users,
  FileText,
  Hash,
  MessageSquare,
  <PERSON>older,
  <PERSON><PERSON>,
} from "lucide-react"

import { NavMain } from "@/components/themes/nav-main"
import { NavProjects } from "@/components/themes/nav-projects"
import { NavUser } from "@/components/themes/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// Blog application data
const data = {
  user: {
    name: "Blog User",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  teams: [
    {
      name: "Centrix",
      logo: BookOpen,
      plan: "Analytic",
    },
  ],
  navMain: [
   /*  {
      title: "Dashboard",
      url: "/",
      icon: Home,
      isActive: true,
      items: [
        {
          title: "Overview",
          url: "/",
        },
      ],
    }, */
    /* {
      title: "Blog",
      url: "/posts",
      icon: FileText,
      items: [
        {
          title: "All Posts",
          url: "/posts",
        },
        {
          title: "Create Post",
          url: "/posts/create",
        },
        {
          title: "Categories",
          url: "/categories",
        },
      ],
    }, */
    /* {
      title: "Content",
      url: "/content",
      icon: Edit3,
      items: [
        {
          title: "Tags",
          url: "/tags",
        },
        {
          title: "Comments",
          url: "/comments",
        },
      ],
    } */,
    {
      title: "Legacy Crawler",
      url: "/crawler",
      icon: Bot,
      items: [
        {
          title: "Dashboard",
          url: "/crawler",
        },
        {
          title: "Create Task",
          url: "/crawler/create",
        },
        {
          title: "Task List",
          url: "/crawler/tasks",
        },
        {
          title: "Results",
          url: "/crawler/results",
        },
        {
          title: "Task History",
          url: "/crawler/history",
        },
      ],
    },
    {
      title: "Actor System",
      url: "/actor",
      icon: Users,
      items: [
        {
          title: "Dashboard",
          url: "/actor",
        },
        {
          title: "Accounts",
          url: "/actor/accounts",
        },
        {
          title: "Tasks",
          url: "/actor/tasks",
        },
        {
          title: "Data",
          url: "/actor/data",
        },
        {
          title: "Sessions",
          url: "/actor/sessions",
        },
      ],
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings2,
      items: [
        {
          title: "Profile",
          url: "/profile",
        },
        {
          title: "Account",
          url: "/account",
        },
      ],
    },
  ],
  projects: [
    /* {
      name: "Draft Posts",
      url: "/posts/drafts",
      icon: BookOpen,
    },
    {
      name: "Categories",
      url: "/categories",
      icon: Folder,
    },
    {
      name: "Comments",
      url: "/comments",
      icon: MessageSquare,
    },
    {
      name: "Tags",
      url: "/tags",
      icon: Hash,
    }, */
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
