import { api } from "@/lib/axios";
import type { HealthStatus, TikTokTask, TaskMetrics, ScrapedData, ScrapedDataSummary, TaskScrapedData } from "@/lib/types/crawler";

// The baseURL in axios.ts is already set to http://localhost:8000/api
// So we should use a relative path without /api prefix
const BASE_URL = "/crawler";

// Health Check
export async function getHealthStatus(): Promise<HealthStatus> {
    const response = await api.get(`${BASE_URL}/health/`);
    return response.data;
}

// Task Management
export async function getTikTokTasks(): Promise<TikTokTask[]> {
    const response = await api.get(`${BASE_URL}/tasks/`);
    // Handle paginated response from Django REST framework
    return response.data.results || response.data;
}

export async function getTaskHistory(): Promise<{ tasks: TikTokTask[] }> {
    const response = await api.get(`${BASE_URL}/tasks/history/`);
    return response.data;
}

export async function getTaskStatistics(): Promise<TaskMetrics> {
    const response = await api.get(`${BASE_URL}/tasks/stats/`);
    return response.data;
}

export interface ScrapeParams {
    job_name: string;
    start_date?: string;
    end_date?: string;

    max_items?: number;
}

// Scraping Operations
export async function scrapeVideo(videoUrls: string[], params: ScrapeParams): Promise<{ task_id: string }> {
    const response = await api.post(`${BASE_URL}/tasks/`, {
        start_urls: videoUrls,
        scrape_type: 'VIDEO',
        ...params
    });
    return { task_id: response.data.id };
}

export async function scrapeUser(usernames: string[], params: ScrapeParams): Promise<{ task_id: string }> {
    const response = await api.post(`${BASE_URL}/tasks/`, {
        start_urls: usernames,
        scrape_type: 'USER',
        ...params
    });
    return { task_id: response.data.id };
}

export async function searchTikTok(keywords: string[], params: ScrapeParams): Promise<{ task_id: string }> {
    const response = await api.post(`${BASE_URL}/tasks/`, {
        start_urls: keywords,
        scrape_type: 'SEARCH',
        ...params
    });
    return { task_id: response.data.id };
}

// Task Operations
export async function getTask(taskId: number): Promise<TikTokTask> {
    const response = await api.get(`${BASE_URL}/tasks/${taskId}/`);
    return response.data;
}

export async function updateTask(taskId: number, data: Partial<TikTokTask>): Promise<TikTokTask> {
    const response = await api.patch(`${BASE_URL}/tasks/${taskId}/`, data);
    return response.data;
}

export async function retryTask(taskId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/retry/`);
    return response.data;
}

export async function cancelTask(taskId: number): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/${taskId}/cancel/`);
    return response.data;
}

export async function deleteTask(taskId: number): Promise<void> {
    await api.delete(`${BASE_URL}/tasks/${taskId}/`);
}

export async function bulkDeleteTasks(taskIds: number[]): Promise<{ message: string }> {
    const response = await api.post(`${BASE_URL}/tasks/bulk_delete/`, { task_ids: taskIds });
    return response.data;
}

// Task Status
export async function getVideoStatus(taskId: string): Promise<any> {
    const response = await api.get(`${BASE_URL}/tasks/${taskId}/`);
    return response.data;
}

export async function getUserStatus(taskId: string): Promise<any> {
    const response = await api.get(`${BASE_URL}/tasks/${taskId}/`);
    return response.data;
}

// Scraped Data Operations
export async function getScrapedData(params?: {
    task_id?: number;
    data_type?: 'VIDEO' | 'USER' | 'SEARCH_RESULT';
    page?: number;
    page_size?: number;
}): Promise<{ results: ScrapedData[]; count: number; next: string | null; previous: string | null }> {
    const queryParams = new URLSearchParams();
    if (params?.task_id) queryParams.append('task_id', params.task_id.toString());
    if (params?.data_type) queryParams.append('data_type', params.data_type);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());
    
    const response = await api.get(`${BASE_URL}/scraped-data/?${queryParams.toString()}`);
    return response.data;
}

export async function getScrapedDataByTask(taskId: number): Promise<TaskScrapedData> {
    const response = await api.get(`${BASE_URL}/scraped-data/by_task/?task_id=${taskId}`);
    return response.data;
}

export async function getScrapedDataSummary(): Promise<ScrapedDataSummary> {
    const response = await api.get(`${BASE_URL}/scraped-data/summary/`);
    return response.data;
}