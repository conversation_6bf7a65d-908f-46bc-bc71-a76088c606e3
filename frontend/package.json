{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.6", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@shadcn/ui": "^0.0.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.19.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.0", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.4.0", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}