'use client';

import { useEffect, useState } from "react";
// New Actor System Components
import { ActorLoginForm } from "@/components/actor/ActorLoginForm";
import { AccountSelector } from "@/components/actor/AccountSelector";
import { EnhancedContentSearch } from "@/components/actor/EnhancedContentSearch";
// Enhanced Components
import { EnhancedAccountList } from "@/components/actor/EnhancedAccountList";
import { SessionMonitor } from "@/components/actor/SessionMonitor";
import { TikTokLoginForm } from "@/components/actor/TikTokLoginForm";
// Using Enhanced versions only
import { ModernTaskManager } from "@/components/actor/ModernTaskManager";
import { EnhancedSessionManager } from "@/components/actor/EnhancedSessionManager";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, AlertCircle, TrendingUp, Users, Zap, Settings } from "lucide-react";
// API imports
import { getActorAccounts, getActorTasks, type ActorAccount, type ActorTask } from "@/lib/api/actor-system";

export default function ActorDashboard() {
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);
    const [loginSuccess, setLoginSuccess] = useState<any>(null);
    // Removed showContentDashboard - using Enhanced components with built-in dashboard
    const [searchResults, setSearchResults] = useState<any>(null);
    const [activeKeywords, setActiveKeywords] = useState<string[]>([]);
    const [activeSessions, setActiveSessions] = useState<any[]>([]);

    // New Actor System state
    const [actorAccounts, setActorAccounts] = useState<ActorAccount[]>([]);
    const [selectedAccount, setSelectedAccount] = useState<ActorAccount | null>(null);
    const [showNewSystem, setShowNewSystem] = useState(true);

    // Check if user is already logged in on component mount
    useEffect(() => {
        const checkAuthStatus = () => {
            const token = localStorage.getItem('access_token');
            const user = localStorage.getItem('user');
            if (token && user) {
                setLoginSuccess(JSON.parse(user));
                console.log('User already authenticated:', JSON.parse(user));
            }
        };

        checkAuthStatus();
        loadActorAccounts();
    }, []);

    const loadActorAccounts = async () => {
        try {
            const accounts = await getActorAccounts();
            setActorAccounts(accounts);

            // Auto-select first account if available
            if (accounts.length > 0 && !selectedAccount) {
                setSelectedAccount(accounts[0]);
            }
        } catch (err) {
            console.error('Error loading actor accounts:', err);
        }
    };

    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            const [accountsData, tasksData] = await Promise.all([
                getActorAccounts(),
                getActorTasks().catch(() => []) // Fallback to empty array if tasks fail
            ]);

            setAccounts(accountsData);
            setTasks(tasksData);
        } catch (err) {
            setError('Failed to fetch actor data');
            console.error('Error fetching actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = async () => {
        await fetchData(); // Use the existing fetchData function
    };

    const handleLoginSuccess = (result: any) => {
        setLoginSuccess(result);
        refreshData(); // Refresh data after successful login
    };

    const handleLoginError = (error: string) => {
        console.error('Login error:', error);
        setLoginSuccess(null);
    };

    const handleLogout = () => {
        // Clear authentication tokens
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');

        // Reset state
        setLoginSuccess(null);
        setSearchResults(null);

        console.log('User logged out');
    };

    const handleSearchComplete = (results: any) => {
        setSearchResults(results);
        setActiveKeywords(results.keywords || []);
    };

    const handleSessionChange = (sessions: any[]) => {
        setActiveSessions(sessions);
    };

    useEffect(() => {
        fetchData();

        // Refresh data every 30 seconds
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[200px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">
                        {showNewSystem ? 'Actor Dashboard' : 'Legacy TikTok System'}
                    </h1>
                    <p className="text-muted-foreground">
                        {showNewSystem
                            ? 'Multi-platform social media scraping and automation system'
                            : 'Legacy TikTok-specific scraping functionality'
                        }
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant={showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(true)}
                    >
                        <Zap className="h-4 w-4 mr-2" />
                        Actor System
                    </Button>
                    <Button
                        variant={!showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(false)}
                    >
                        Legacy TikTok
                    </Button>
                </div>
                {loginSuccess && (
                    <div className="flex items-center gap-4">
                        <Badge variant="default" className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4" />
                            Login Active
                        </Badge>
                        <Button variant="outline" size="sm" onClick={handleLogout}>
                            Logout
                        </Button>
                    </div>
                )}
            </div>

            {/* Main Content Tabs */}
            <Tabs defaultValue={showNewSystem ? "actor-search" : "search"} className="w-full">
                <TabsList className={`grid w-full ${showNewSystem ? 'grid-cols-4' : 'grid-cols-6'}`}>
                    {showNewSystem ? (
                        <>
                            <TabsTrigger value="actor-search">Smart Search</TabsTrigger>
                            <TabsTrigger value="actor-accounts">My Accounts</TabsTrigger>
                            <TabsTrigger value="actor-setup">Add Account</TabsTrigger>
                            <TabsTrigger value="legacy">Legacy TikTok</TabsTrigger>
                        </>
                    ) : (
                        <>
                            <TabsTrigger value="search">TikTok Search</TabsTrigger>
                            <TabsTrigger value="tasks">Tasks</TabsTrigger>
                            <TabsTrigger value="sessions">Sessions</TabsTrigger>
                            <TabsTrigger value="login">TikTok Login</TabsTrigger>
                            <TabsTrigger value="system">System</TabsTrigger>
                        </>
                    )}
                </TabsList>

                {/* NEW ACTOR SYSTEM TABS */}
                {showNewSystem && (
                    <>
                        {/* Enhanced Search Tab */}
                        <TabsContent value="actor-search" className="space-y-6">
                            <EnhancedContentSearch
                                onSearchComplete={(results) => {
                                    console.log('Search completed:', results);
                                    setSearchResults(results);
                                }}
                                onSearchStart={() => {
                                    console.log('Search started');
                                }}
                                onError={(error) => {
                                    console.error('Search error:', error);
                                    setError(error);
                                }}
                                defaultAccount={selectedAccount || undefined}
                            />
                        </TabsContent>

                        {/* Actor Accounts Tab */}
                        <TabsContent value="actor-accounts" className="space-y-6">
                            <div className="grid gap-6 lg:grid-cols-2">
                                <AccountSelector
                                    selectedAccountId={selectedAccount?.id}
                                    onAccountSelect={setSelectedAccount}
                                    requireAuthentication={false}
                                />

                                <Card>
                                    <CardHeader>
                                        <CardTitle>Account Statistics</CardTitle>
                                        <CardDescription>
                                            Overview of your connected accounts
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            <div className="flex justify-between">
                                                <span>Total Accounts:</span>
                                                <Badge>{actorAccounts.length}</Badge>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Active Sessions:</span>
                                                <Badge variant="secondary">
                                                    {actorAccounts.filter(a => a.session_valid).length}
                                                </Badge>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Platforms:</span>
                                                <Badge variant="outline">
                                                    {new Set(actorAccounts.map(a => a.platform)).size}
                                                </Badge>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        {/* Actor Setup Tab */}
                        <TabsContent value="actor-setup" className="space-y-6">
                            <ActorLoginForm
                                onLoginSuccess={(result) => {
                                    console.log('Login successful:', result);
                                    loadActorAccounts();
                                }}
                                onLoginError={(error) => {
                                    console.error('Login error:', error);
                                    setError(error);
                                }}
                                onAccountCreated={(account) => {
                                    console.log('Account created:', account);
                                    loadActorAccounts();
                                }}
                            />
                        </TabsContent>

                        {/* Legacy System Tab */}
                        <TabsContent value="legacy" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Legacy TikTok System</CardTitle>
                                    <CardDescription>
                                        Access the original TikTok-only functionality for backward compatibility
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                            <p className="text-sm text-yellow-800">
                                                <strong>Note:</strong> The legacy system is maintained for backward compatibility.
                                                We recommend using the new Actor system for better features and multi-platform support.
                                            </p>
                                        </div>
                                        <Button
                                            onClick={() => setShowNewSystem(false)}
                                            className="w-full"
                                            variant="outline"
                                        >
                                            Switch to Legacy TikTok System
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </>
                )}

                {/* LEGACY SYSTEM TABS */}
                {!showNewSystem && (
                    <>
                        {/* Content Search Tab */}
                <TabsContent value="search" className="space-y-6">
                    {loginSuccess ? (
                        <EnhancedContentSearch
                            onSearchComplete={handleSearchComplete}
                        />
                    ) : (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5" />
                                    Login Required
                                </CardTitle>
                                <CardDescription>
                                    Please login to TikTok first to start searching for content
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">
                                    Switch to the "Login" tab to authenticate and start searching for any keyword content.
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Dashboard functionality is now integrated into EnhancedContentSearch */}

                {/* Tasks Tab */}
                <TabsContent value="tasks" className="space-y-6">
                    <ModernTaskManager
                        accounts={accounts}
                        onAccountsChange={fetchData}
                    />
                </TabsContent>

                {/* Sessions Tab */}
                <TabsContent value="sessions" className="space-y-6">
                    <EnhancedSessionManager
                        onSessionChange={handleSessionChange}
                    />
                </TabsContent>

                {/* Login Tab */}
                <TabsContent value="login" className="space-y-6">
                    <div className="max-w-2xl mx-auto">
                        <TikTokLoginForm
                            onLoginSuccess={handleLoginSuccess}
                            onLoginError={handleLoginError}
                            showTestMode={true}
                        />

                        {loginSuccess && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-green-600">
                                        <CheckCircle className="h-5 w-5" />
                                        Login Successful!
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2 text-sm">
                                        <p><strong>Status:</strong> ✅ Connected to TikTok</p>
                                        <p><strong>Session:</strong> {loginSuccess.cookies_count || 0} cookies active</p>
                                        <p><strong>Active Sessions:</strong> {activeSessions.filter(s => s.status === 'active').length}</p>
                                        <p><strong>Ready for:</strong> Keyword-based content discovery and task management</p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </TabsContent>

                {/* System Overview Tab */}
                <TabsContent value="system" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* System Status */}
                        <Card className="p-6">
                            <h3 className="text-lg font-semibold mb-4">System Status</h3>
                            <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">API Status</span>
                                    <Badge className="bg-green-100 text-green-800">
                                        <CheckCircle className="w-3 h-3 mr-1" />
                                        Online
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">Database</span>
                                    <Badge className="bg-green-100 text-green-800">
                                        <CheckCircle className="w-3 h-3 mr-1" />
                                        Connected
                                    </Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">Authentication</span>
                                    <Badge className="bg-green-100 text-green-800">
                                        <CheckCircle className="w-3 h-3 mr-1" />
                                        Active
                                    </Badge>
                                </div>
                            </div>
                        </Card>

                        {/* Quick Statistics */}
                        <Card className="p-6">
                            <h3 className="text-lg font-semibold mb-4">Quick Statistics</h3>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-center">
                                    <p className="text-sm text-gray-500">Total Accounts</p>
                                    <p className="text-xl font-semibold">{accounts.length}</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-sm text-gray-500">Active Accounts</p>
                                    <p className="text-xl font-semibold">
                                        {accounts.filter(a => a.is_active).length}
                                    </p>
                                </div>
                                <div className="text-center">
                                    <p className="text-sm text-gray-500">Total Tasks</p>
                                    <p className="text-xl font-semibold">{tasks.length}</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-sm text-gray-500">Platforms</p>
                                    <p className="text-xl font-semibold">
                                        {new Set(accounts.map(a => a.platform)).size}
                                    </p>
                                </div>
                            </div>
                        </Card>
                    </div>
                </TabsContent>

                {/* Management Tab */}
                <TabsContent value="management" className="space-y-6">
                    <div className="grid gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    TikTok Accounts
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <EnhancedAccountList accounts={accounts} onAccountsChange={refreshData} />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Session Monitor
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <SessionMonitor />
                            </CardContent>
                        </Card>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Task Management</CardTitle>
                            <CardDescription>
                                Create and manage your automation tasks
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ModernTaskManager
                                accounts={accounts}
                                onAccountsChange={fetchData}
                            />
                        </CardContent>
                    </Card>
                </TabsContent>
                    </>
                )}
            </Tabs>
        </div>
    );
}