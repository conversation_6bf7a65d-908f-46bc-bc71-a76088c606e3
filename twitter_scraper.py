"""
Twitter Scraper Module - Uses Selenium and Chrome WebDriver for real-time Twitter scraping
"""
import logging
import time
import re
import os
import json
import random
from datetime import datetime, timedelta
import sys

# We'll access scrape_stats through a global variable that will be set by app.py
# This avoids circular imports
scrape_stats = {}
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
try:
    from selenium.webdriver.chrome.service import Service
except ImportError as e:
    import selenium
    raise ImportError(f"Your Selenium version is too old. Please upgrade to Selenium 4.x or newer. Current version: {getattr(selenium, '__version__', 'unknown')}. Error: {e}")

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException,
    StaleElementReferenceException,
    NoSuchElementException,
    ElementClickInterceptedException
)
import chromedriver_autoinstaller
from bs4 import BeautifulSoup
import pandas as pd

# Configure logging
logger = logging.getLogger(__name__)
# Don't set basicConfig here as it will be set in app.py
# Just make sure this logger is properly configured
logger.setLevel(logging.DEBUG)

def retry_on_exception(func):
    """Decorator to retry operations on failure"""
    def wrapper(self, *args, **kwargs):
        for attempt in range(self.max_retries):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"Failed after {self.max_retries} attempts: {e}")
                    raise
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying...")
                time.sleep(2 ** attempt)  # Exponential backoff
        return None
    return wrapper

class TwitterScraper:
    def __init__(self, max_retries=3, delay_between_scrolls=3, rate_limit_delay=2, email=None, username=None, password=None):
        """Initialize the Twitter scraper with Selenium and Chrome WebDriver

        Args:
            max_retries (int): Maximum number of retries for failed operations
            delay_between_scrolls (int): Delay in seconds between scroll operations
            rate_limit_delay (int): Base delay in seconds between requests
            email (str): Twitter account email
            username (str): Twitter account username
            password (str): Twitter account password
        """
        self.tweets = []
        self.driver = None
        self.wait = None
        self.max_retries = max_retries
        self.delay_between_scrolls = delay_between_scrolls
        self.rate_limit_delay = rate_limit_delay
        self.last_request_time = 0
        self.email = email
        self.username = username
        self.password = password
        self.is_logged_in = False

        # Ensure chromedriver is installed and updated
        chromedriver_autoinstaller.install()

        # Configure Chrome options
        self.chrome_options = Options()

        # For debugging, let's use non-headless mode first
        # self.chrome_options.add_argument('--headless=new')  # Use new headless mode

        # Basic Chrome options
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')

        # Disable automation detection
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)

        # Set a realistic user agent
        self.chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')

        # Enable JavaScript
        self.chrome_options.add_argument('--enable-javascript')

        # Disable images to speed up loading
        self.chrome_options.add_argument('--blink-settings=imagesEnabled=false')

        # Disable extensions
        self.chrome_options.add_argument('--disable-extensions')

        # Disable infobars
        self.chrome_options.add_argument('--disable-infobars')

        # Add additional required preferences
        self.chrome_options.add_experimental_option('prefs', {
            'profile.default_content_setting_values.notifications': 2,
            'profile.default_content_settings.popups': 0,
            'download.prompt_for_download': False,
            'download.directory_upgrade': True,
            'safebrowsing.enabled': True,
            'credentials_enable_service': True,
            'profile.password_manager_enabled': True,
            'intl.accept_languages': 'en-US,en',
            'javascript.enabled': True
        })

        # Add dynamic delay calculation based on response times
        self.min_delay = rate_limit_delay
        self.max_delay = rate_limit_delay * 3
        self.current_delay = rate_limit_delay
        self.failed_requests = 0

    def apply_rate_limiting(self):
        """Apply dynamic rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.current_delay:
            sleep_time = self.current_delay - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)

        # Adjust delay based on failed requests
        if self.failed_requests > 0:
            self.current_delay = min(self.max_delay, self.current_delay * 1.5)
            self.failed_requests = 0
        else:
            self.current_delay = max(self.min_delay, self.current_delay * 0.8)

        self.last_request_time = time.time()

    def login_to_twitter(self):
        """Log in to Twitter using provided credentials with enhanced error handling"""
        if not self.email or not self.username or not self.password:
            logger.warning("Incomplete login credentials provided, but Twitter requires login to view tweets")
            return False

        try:
            logger.info("Attempting to log in to Twitter...")
            self.driver.get('https://twitter.com/login')
            time.sleep(3)  # Wait for page load

            # Take a screenshot for debugging
            try:
                screenshot_path = "login_page_initial.png"
                self.driver.save_screenshot(screenshot_path)
                logger.debug(f"Saved login page screenshot to {screenshot_path}")
            except Exception as ss_error:
                logger.warning(f"Could not save login screenshot: {ss_error}")

            # Log current URL and page title
            logger.debug(f"Login page URL: {self.driver.current_url}")
            logger.debug(f"Login page title: {self.driver.title}")

            # Wait for and fill in username/email
            try:
                logger.debug("Looking for username input field...")

                # Try multiple selectors for the username field
                username_selectors = [
                    'input[autocomplete="username"]',
                    'input[name="text"]',
                    'input[type="text"]'
                ]

                username_input = None
                for selector in username_selectors:
                    try:
                        logger.debug(f"Trying selector: {selector}")
                        username_input = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        if username_input:
                            logger.debug(f"Found username input with selector: {selector}")
                            break
                    except:
                        continue

                if not username_input:
                    logger.error("Could not find username input field")
                    return False

                # Clear the field and enter email
                username_input.clear()
                username_input.send_keys(self.email)
                logger.debug(f"Entered email: {self.email}")
                time.sleep(1)

                # Take a screenshot after entering email
                self.driver.save_screenshot("after_email_entry.png")

                # Click the "Next" button - try multiple approaches
                next_button = None
                next_button_selectors = [
                    "//span[text()='Next']",
                    "//span[contains(text(),'Next')]",
                    "//div[@role='button' and contains(., 'Next')]",
                    "//div[@data-testid='ocfEnterTextNextButton']"
                ]

                for selector in next_button_selectors:
                    try:
                        logger.debug(f"Looking for Next button with selector: {selector}")
                        next_button = self.driver.find_element(By.XPATH, selector)
                        if next_button:
                            logger.debug(f"Found Next button with selector: {selector}")
                            break
                    except:
                        continue

                if not next_button:
                    logger.error("Could not find Next button")
                    return False

                next_button.click()
                logger.debug("Clicked Next button")
                time.sleep(2)

                # Take a screenshot after clicking Next
                self.driver.save_screenshot("after_next_button.png")

                # Now look for the username field (Twitter sometimes asks for username after email)
                try:
                    logger.debug("Looking for username input field after email...")

                    # Wait for any input field to appear
                    username_field = None
                    username_field_selectors = [
                        'input[name="text"]',
                        'input[type="text"]',
                        'input[autocomplete="username"]'
                    ]

                    for selector in username_field_selectors:
                        try:
                            logger.debug(f"Trying username selector after email: {selector}")
                            username_field = WebDriverWait(self.driver, 5).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                            )
                            if username_field:
                                logger.debug(f"Found username field with selector: {selector}")
                                break
                        except:
                            continue

                    # If we found a username field, enter the username
                    if username_field:
                        # Clear the field and enter username
                        username_field.clear()
                        username_field.send_keys(self.username)
                        logger.debug(f"Entered username: {self.username}")
                        time.sleep(1)

                        # Take a screenshot after entering username
                        self.driver.save_screenshot("after_username_entry.png")

                        # Press Enter to submit username
                        username_field.send_keys(Keys.RETURN)
                        logger.debug("Pressed Enter key to submit username")
                        time.sleep(3)  # Wait for password field to appear
                except Exception as username_error:
                    logger.warning(f"Error or no username field found after email: {username_error}")
                    # Continue anyway as Twitter doesn't always ask for username

            except Exception as e:
                logger.error(f"Error entering email/username: {e}", exc_info=True)
                return False

            # Wait for and fill in password
            try:
                logger.debug("Looking for password input field...")

                # Try multiple selectors for the password field
                password_selectors = [
                    'input[name="password"]',
                    'input[type="password"]',
                    'input[autocomplete="current-password"]'
                ]

                password_input = None
                for selector in password_selectors:
                    try:
                        logger.debug(f"Trying selector: {selector}")
                        password_input = WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        if password_input:
                            logger.debug(f"Found password input with selector: {selector}")
                            break
                    except:
                        continue

                if not password_input:
                    logger.error("Could not find password input field")
                    return False

                # Clear the field and enter password
                password_input.clear()
                password_input.send_keys(self.password)
                logger.debug("Entered password")
                time.sleep(1)

                # Take a screenshot after entering password
                self.driver.save_screenshot("after_password_entry.png")

                # Click the login button - try multiple approaches
                login_button = None
                login_button_selectors = [
                    "//span[text()='Log in']",
                    "//span[contains(text(),'Log in')]",
                    "//div[@role='button' and contains(., 'Log in')]",
                    "//div[@data-testid='LoginForm_Login_Button']"
                ]

                for selector in login_button_selectors:
                    try:
                        logger.debug(f"Looking for Login button with selector: {selector}")
                        login_button = self.driver.find_element(By.XPATH, selector)
                        if login_button:
                            logger.debug(f"Found Login button with selector: {selector}")
                            break
                    except:
                        continue

                if not login_button:
                    logger.error("Could not find Login button")
                    return False

                login_button.click()
                logger.debug("Clicked Login button")
                time.sleep(5)  # Wait for login to complete

                # Take a screenshot after clicking Login
                self.driver.save_screenshot("after_login_button.png")

            except Exception as e:
                logger.error(f"Error entering password: {e}", exc_info=True)
                return False

            # Verify login success - try multiple indicators
            try:
                logger.debug("Verifying login success...")

                # Check current URL
                current_url = self.driver.current_url
                logger.debug(f"Current URL after login attempt: {current_url}")

                # Take a screenshot after login attempt
                self.driver.save_screenshot("after_login_attempt.png")

                # Try multiple ways to verify login
                login_success = False

                # Method 1: Check if we're redirected away from login page
                if "login" not in current_url:
                    logger.debug("Login appears successful (redirected away from login page)")
                    login_success = True

                # Method 2: Look for elements that only appear when logged in
                login_indicators = [
                    '[data-testid="SideNav_AccountSwitcher_Button"]',
                    '[data-testid="AppTabBar_Home_Link"]',
                    '[data-testid="primaryColumn"]',
                    '.r-1habvwh'  # Common class for Twitter's main content
                ]

                for indicator in login_indicators:
                    try:
                        element = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, indicator))
                        )
                        if element:
                            logger.debug(f"Found logged-in indicator: {indicator}")
                            login_success = True
                            break
                    except:
                        continue

                if login_success:
                    logger.info("Successfully logged in to Twitter")
                    self.is_logged_in = True
                    return True
                else:
                    logger.error("Login verification failed - could not find any logged-in indicators")
                    return False

            except Exception as e:
                logger.error(f"Login verification failed: {e}", exc_info=True)
                return False

        except Exception as e:
            logger.error(f"Login failed: {e}", exc_info=True)
            return False

    @retry_on_exception
    def setup_driver(self):
        """Set up Chrome WebDriver and log in to Twitter"""
        try:
            logger.debug("Setting up Chrome WebDriver")

            # Log Chrome options for debugging
            logger.debug(f"Chrome options: {self.chrome_options.arguments}")

            # Install chromedriver
            logger.debug("Installing/updating chromedriver")
            chromedriver_path = chromedriver_autoinstaller.install()
            logger.debug(f"Chromedriver installed at: {chromedriver_path}")

            # Create service
            logger.debug("Creating Chrome service")
            service = Service(chromedriver_path)

            # Initialize driver
            logger.debug("Initializing Chrome WebDriver")
            self.driver = webdriver.Chrome(service=service, options=self.chrome_options)

            # Set up wait
            logger.debug("Setting up WebDriverWait with 10 second timeout")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome WebDriver initialized successfully")

            # Add additional error checks
            if not self.driver:
                logger.error("Driver object is None after initialization")
                raise Exception("Failed to initialize Chrome WebDriver")

            # Test browser functionality
            logger.debug("Testing browser functionality by loading example.com")
            self.driver.get("https://www.example.com")

            logger.debug(f"Page title: {self.driver.title}")
            if not self.driver.title:
                logger.error("Browser failed to load test page (no title)")
                raise Exception("Browser failed to load test page")

            logger.info("Chrome WebDriver initialized and tested successfully")

            # Attempt to log in if credentials are provided
            if self.email and self.username and self.password:
                logger.debug(f"Attempting to log in with email: {self.email} and username: {self.username}")
                if not self.login_to_twitter():
                    logger.warning("Failed to log in to Twitter, continuing without login")
                else:
                    logger.info("Successfully logged in to Twitter")
            else:
                logger.debug("Incomplete login credentials provided, continuing without login")

            return True
        except Exception as e:
            logger.error(f"Error setting up Chrome WebDriver: {e}", exc_info=True)
            self.failed_requests += 1
            return False

    def close_driver(self):
        """Close Chrome WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                logger.error(f"Error closing Chrome WebDriver: {e}")
            finally:
                self.driver = None
                self.wait = None

    def construct_search_url(self, keyword, start_date=None, end_date=None, username=None, hashtag=None):
        """Construct a Twitter search URL based on parameters"""
        logger.debug("Constructing search URL with parameters:")
        logger.debug(f"  keyword: {keyword}")
        logger.debug(f"  start_date: {start_date}")
        logger.debug(f"  end_date: {end_date}")
        logger.debug(f"  username: {username}")
        logger.debug(f"  hashtag: {hashtag}")

        # Base search URL
        base_url = "https://twitter.com/search?q="

        # Build query parts
        query_parts = []

        # Add keyword
        if keyword:
            # Clean up keyword
            keyword = keyword.strip()
            if keyword:
                logger.debug(f"Adding keyword: {keyword}")
                query_parts.append(keyword)

        # Add username filter
        if username:
            # Clean up username
            username = username.strip()
            if username:
                if username.startswith('@'):
                    username = username[1:]
                logger.debug(f"Adding username filter: from:{username}")
                query_parts.append(f"from:{username}")

        # Add hashtag filter
        if hashtag:
            # Clean up hashtag
            hashtag = hashtag.strip()
            if hashtag:
                if not hashtag.startswith('#'):
                    hashtag = f"#{hashtag}"
                logger.debug(f"Adding hashtag filter: {hashtag}")
                query_parts.append(hashtag)

        # Add date range
        if start_date and end_date:
            logger.debug(f"Adding date range: since:{start_date} until:{end_date}")
            query_parts.append(f"since:{start_date}")
            query_parts.append(f"until:{end_date}")
        elif start_date:
            logger.debug(f"Adding start date: since:{start_date}")
            query_parts.append(f"since:{start_date}")
        elif end_date:
            logger.debug(f"Adding end date: until:{end_date}")
            query_parts.append(f"until:{end_date}")

        # Join query parts
        query = " ".join(query_parts)
        logger.debug(f"Raw query: {query}")

        # Check if query is empty
        if not query.strip():
            logger.warning("Empty query string, using default search")
            query = "twitter"  # Default search term

        # URL encode the query
        import urllib.parse
        encoded_query = urllib.parse.quote_plus(query)
        logger.debug(f"URL encoded query: {encoded_query}")

        # Add filter for latest tweets
        full_url = f"{base_url}{encoded_query}&src=typed_query&f=live"

        logger.info(f"Constructed URL: {full_url}")
        return full_url

    @retry_on_exception
    def extract_tweet_data(self, tweet_element):
        """Extract data from a tweet element with error handling and retries"""
        try:
            self.apply_rate_limiting()

            # Update scrape_stats to show we're extracting tweet data
            scrape_stats['current_processing'] = "Extracting tweet data..."

            tweet_data = {
                'id': '',
                'text': '',
                'date': '',
                'url': '',
                'replies': 0,
                'retweets': 0,
                'likes': 0,
                'author_name': '',
                'author_username': '',
                'author_url': '',
                'author_verified': False
            }

            # Get tweet ID and URL
            try:
                url_elements = tweet_element.find_elements(By.CSS_SELECTOR, 'a[href*="/status/"]')
                for elem in url_elements:
                    href = elem.get_attribute('href')
                    if href and '/status/' in href:
                        tweet_data['url'] = href
                        match = re.search(r'/status/(\d+)', href)
                        if match:
                            tweet_data['id'] = match.group(1)
                            break

                if not tweet_data['id']:
                    logger.warning("Could not extract tweet ID from URL")
                    return None
            except Exception as e:
                logger.error(f"Error extracting tweet URL/ID: {e}")
                return None

            # Get tweet text with retry
            text_found = False
            retry_count = 0
            while not text_found and retry_count < 3:
                try:
                    text_elements = tweet_element.find_elements(By.CSS_SELECTOR, '[data-testid="tweetText"]')
                    if text_elements:
                        tweet_data['text'] = text_elements[0].text.strip()
                        text_found = True
                        # Update current tweet in scrape_stats
                        scrape_stats['current_tweet'] = tweet_data['text']
                    else:
                        # Try alternate selector
                        text_elements = tweet_element.find_elements(By.CSS_SELECTOR, '[lang]')
                        if text_elements:
                            tweet_data['text'] = text_elements[0].text.strip()
                            text_found = True
                            # Update current tweet in scrape_stats
                            scrape_stats['current_tweet'] = tweet_data['text']
                except Exception:
                    retry_count += 1
                    time.sleep(1)

            # Get timestamp with retry
            date_found = False
            retry_count = 0
            while not date_found and retry_count < 3:
                try:
                    time_elements = tweet_element.find_elements(By.CSS_SELECTOR, 'time')
                    if time_elements:
                        tweet_data['date'] = time_elements[0].get_attribute('datetime')
                        date_found = True
                except Exception:
                    retry_count += 1
                    time.sleep(1)

            if not date_found:
                tweet_data['date'] = datetime.now().isoformat()

            # Get author info with multiple retries and selectors
            author_found = False
            retry_count = 0

            while not author_found and retry_count < 3:
                try:
                    # Try multiple selectors for author information
                    author_selectors = [
                        '[data-testid="User-Name"]',
                        '[data-testid="Tweet-User-Name"]',
                        'a[role="link"] span span'  # Common Twitter username pattern
                    ]

                    for selector in author_selectors:
                        author_elements = tweet_element.find_elements(By.CSS_SELECTOR, selector)
                        for author_element in author_elements:
                            text = author_element.text
                            if text and '@' in text:
                                name_parts = text.split('\n')
                                if len(name_parts) >= 2:
                                    tweet_data['author_name'] = name_parts[0].strip()
                                    username = name_parts[1].replace('@', '').strip()
                                    if username:
                                        tweet_data['author_username'] = username
                                        tweet_data['author_url'] = f"https://twitter.com/{username}"
                                        author_found = True
                                        break

                        if author_found:
                            break

                    # Check for verification with multiple badge types
                    try:
                        verified_selectors = [
                            '[data-testid="icon-verified"]',
                            '[aria-label*="verified"]',
                            'svg[aria-label*="Verified"]'
                        ]
                        for selector in verified_selectors:
                            verified_badges = tweet_element.find_elements(By.CSS_SELECTOR, selector)
                            if verified_badges:
                                tweet_data['author_verified'] = True
                                break
                    except:
                        tweet_data['author_verified'] = False

                    if author_found:
                        break

                except Exception as e:
                    logger.warning(f"Author extraction attempt {retry_count + 1} failed: {e}")
                    retry_count += 1
                    time.sleep(1)

            if not author_found:
                logger.error("Failed to extract author information")
                tweet_data['author_name'] = ''
                tweet_data['author_username'] = ''
                tweet_data['author_url'] = ''
                tweet_data['author_verified'] = False

            # Get engagement metrics
            metrics = {'replies': 0, 'retweets': 0, 'likes': 0, 'views': 0}
            try:
                # Try to get metrics using specific data-testid attributes first
                specific_metrics = {
                    'replies': ['reply-count', 'reply', 'comment'],
                    'retweets': ['retweet-count', 'retweeted', 'share'],
                    'likes': ['like-count', 'favorite', 'heart'],
                    'views': ['view-count', 'analytics', 'impression']
                }

                for metric_name, selectors in specific_metrics.items():
                    for selector in selectors:
                        try:
                            # Try data-testid approach
                            elements = tweet_element.find_elements(By.CSS_SELECTOR, f'[data-testid*="{selector}"]')

                            # If not found, try aria-label approach
                            if not elements:
                                elements = tweet_element.find_elements(By.CSS_SELECTOR, f'[aria-label*="{selector}"]')

                            # If still not found, try looking for group elements
                            if not elements:
                                elements = tweet_element.find_elements(By.CSS_SELECTOR, f'div[role="group"] div')

                            for elem in elements:
                                try:
                                    # Get text content
                                    value_text = elem.text.strip()
                                    if not value_text or not any(c.isdigit() for c in value_text):
                                        continue

                                    # Convert K/M to numbers
                                    if 'K' in value_text.upper():
                                        value = float(value_text.upper().replace('K', '').strip()) * 1000
                                    elif 'M' in value_text.upper():
                                        value = float(value_text.upper().replace('M', '').strip()) * 1000000
                                    else:
                                        value = float(''.join(c for c in value_text if c.isdigit() or c == '.')) if any(c.isdigit() for c in value_text) else 0

                                    # Only update if we found a value and it's larger than what we have
                                    if value > metrics[metric_name]:
                                        metrics[metric_name] = int(value)
                                        logger.debug(f"Found {metric_name}: {value} from element: {value_text}")
                                except Exception as inner_e:
                                    logger.debug(f"Error parsing metric value: {inner_e}")
                                    continue
                        except Exception as selector_e:
                            logger.debug(f"Error with selector {selector}: {selector_e}")
                            continue

                # If we still don't have metrics, try a more general approach
                if metrics['replies'] == 0 and metrics['retweets'] == 0 and metrics['likes'] == 0:
                    logger.debug("Using general approach for metrics extraction")

                    # Look for all possible engagement elements
                    engagement_selectors = [
                        '[data-testid$="-count"]',
                        '[data-testid="app-text-transition-container"]',
                        'span[style*="text-align"]',
                        'div[role="group"] div'
                    ]

                    for selector in engagement_selectors:
                        elements = tweet_element.find_elements(By.CSS_SELECTOR, selector)
                        for elem in elements:
                            try:
                                value = elem.text.strip()
                                if not value or not any(c.isdigit() for c in value):
                                    continue

                                # Convert K/M to numbers
                                if 'K' in value.upper():
                                    value = float(value.upper().replace('K', '').strip()) * 1000
                                elif 'M' in value.upper():
                                    value = float(value.upper().replace('M', '').strip()) * 1000000
                                else:
                                    value = float(''.join(c for c in value if c.isdigit() or c == '.')) if any(c.isdigit() for c in value) else 0

                                # Try to determine metric type from context
                                try:
                                    parent = elem.find_element(By.XPATH, '..')
                                    parent_html = parent.get_attribute('outerHTML').lower()

                                    if any(word in parent_html for word in ['reply', 'comment', 'respond']):
                                        metrics['replies'] = int(value)
                                    elif any(word in parent_html for word in ['retweet', 'share']):
                                        metrics['retweets'] = int(value)
                                    elif any(word in parent_html for word in ['like', 'favorite', 'heart']):
                                        metrics['likes'] = int(value)
                                    elif any(word in parent_html for word in ['view', 'impression', 'analytics']):
                                        metrics['views'] = int(value)
                                except:
                                    # If we can't determine the type, make an educated guess based on position
                                    # This is a fallback and might not be accurate
                                    pass
                            except Exception as elem_e:
                                logger.debug(f"Error processing element: {elem_e}")
                                continue

                logger.debug(f"Extracted metrics: replies={metrics['replies']}, retweets={metrics['retweets']}, likes={metrics['likes']}, views={metrics['views']}")

            except Exception as e:
                logger.error(f"Error extracting metrics: {e}", exc_info=True)

            # Update tweet data with metrics
            tweet_data.update(metrics)

            # Check for media content
            try:
                media_elements = tweet_element.find_elements(By.CSS_SELECTOR, '[data-testid="tweetPhoto"], [data-testid="tweetVideo"]')
                tweet_data['has_media'] = len(media_elements) > 0
                tweet_data['media_count'] = len(media_elements)
            except:
                tweet_data['has_media'] = False
                tweet_data['media_count'] = 0

            return tweet_data

        except Exception as e:
            logger.error(f"Error extracting tweet data: {e}")
            return None

    def start_scraping(self, search_url, max_tweets=100, should_stop_callback=None):
        """Scrape tweets from Twitter search results with enhanced error handling

        Args:
            search_url: The URL to scrape
            max_tweets: Maximum number of tweets to collect
            should_stop_callback: Optional function that returns True if scraping should stop
        """
        try:
            logger.debug("Starting scraping process")
            if not self.setup_driver():
                logger.error("Failed to set up Chrome WebDriver")
                raise Exception("Failed to set up Chrome WebDriver")

            logger.debug(f"Navigating to search URL: {search_url}")
            self.driver.get(search_url)
            logger.info(f"Successfully navigated to {search_url}")

            # Take a screenshot for debugging
            try:
                screenshot_path = "debug_screenshot.png"
                self.driver.save_screenshot(screenshot_path)
                logger.debug(f"Saved debug screenshot to {screenshot_path}")
            except Exception as ss_error:
                logger.warning(f"Could not save screenshot: {ss_error}")

            # Log page source for debugging
            try:
                logger.debug(f"Page title: {self.driver.title}")
                logger.debug(f"Current URL: {self.driver.current_url}")
                page_source = self.driver.page_source
                logger.debug(f"Page source length: {len(page_source)} characters")

                # Log a small portion of the page source
                source_preview = page_source[:500] + "..." if len(page_source) > 500 else page_source
                logger.debug(f"Page source preview: {source_preview}")
            except Exception as ps_error:
                logger.warning(f"Could not log page source: {ps_error}")

            tweets_found = 0
            scroll_attempts = 0
            max_scroll_attempts = max_tweets * 2  # Allow more scroll attempts than needed

            logger.info(f"Starting to scrape up to {max_tweets} tweets")

            while tweets_found < max_tweets and scroll_attempts < max_scroll_attempts:
                try:
                    # Check if we should stop or pause
                    if should_stop_callback and should_stop_callback():
                        logger.info("Stopping scraping process due to external request")
                        break

                    # Force stop if we've reached max tweets
                    if tweets_found >= max_tweets:
                        logger.info(f"Reached maximum number of tweets ({max_tweets}), stopping scraping")
                        # Update progress to 100%
                        scrape_stats['progress'] = 100
                        # Force status to completed to stop the scraper
                        scrape_stats['status'] = 'completed'
                        # Set end time
                        scrape_stats['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        # Update records count
                        scrape_stats['records'] = len(self.tweets)
                        # Return tweets immediately to stop the scraping process
                        return self.tweets

                    # Check if we're paused - if so, wait until resumed
                    while scrape_stats.get('status') == 'paused':
                        logger.debug("Scraping paused, waiting...")
                        time.sleep(1)  # Wait 1 second before checking again

                    logger.debug(f"Scroll attempt {scroll_attempts + 1}/{max_scroll_attempts}")

                    # Check if we're on the login page
                    current_url = self.driver.current_url
                    if "login" in current_url:
                        logger.warning("Redirected to login page. Twitter requires authentication.")
                        # Try to login if credentials are provided
                        if self.username and self.password:
                            logger.info("Attempting to log in with provided credentials...")
                            login_success = self.login_to_twitter()
                            if not login_success:
                                logger.error("Login failed. Cannot continue scraping.")
                                raise Exception("Twitter login required but login failed")
                        else:
                            logger.error("Twitter login required but no credentials provided")
                            raise Exception("Twitter login required but no credentials provided")

                    # Wait for tweets to load
                    logger.debug("Waiting for tweet elements to load")
                    tweet_elements = self.wait.until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, 'article[data-testid="tweet"]'))
                    )

                    # Update total scanned count in scrape_stats
                    current_total = scrape_stats.get('total_scanned', 0)
                    tweets_detected = len(tweet_elements)
                    scrape_stats['total_scanned'] = current_total + tweets_detected

                    # Add tweets detected info to scrape_stats
                    scrape_stats['tweets_detected'] = tweets_detected
                    scrape_stats['current_scan_info'] = f"Found {tweets_detected} tweet elements on page"

                    # Calculate scan rate
                    current_time = time.time()
                    # Use static time difference for rate calculation
                    time_diff = 5.0  # Calculate rate over 5 seconds

                    # Store the current scan count for rate calculation
                    if 'last_scan_time' in scrape_stats and 'last_scan_count' in scrape_stats:
                        elapsed = current_time - scrape_stats['last_scan_time']
                        if elapsed >= time_diff:
                            count_diff = scrape_stats['total_scanned'] - scrape_stats['last_scan_count']
                            if count_diff > 0:
                                scan_rate = round(count_diff / elapsed, 1)
                                scrape_stats['scan_rate'] = scan_rate
                            # Reset counters
                            scrape_stats['last_scan_count'] = scrape_stats['total_scanned']
                            scrape_stats['last_scan_time'] = current_time
                    else:
                        # Initialize scan rate tracking
                        scrape_stats['last_scan_time'] = current_time
                        scrape_stats['last_scan_count'] = scrape_stats['total_scanned']

                    logger.debug(f"Found {len(tweet_elements)} tweet elements on the page (total scanned: {scrape_stats['total_scanned']})")

                    for i, tweet in enumerate(tweet_elements):
                        if tweets_found >= max_tweets:
                            logger.debug("Reached maximum number of tweets, breaking loop")
                            break

                        try:
                            logger.debug(f"Extracting data from tweet element {i+1}/{len(tweet_elements)}")
                            # Update current tweet being processed in scrape_stats
                            scrape_stats['current_processing'] = f"Processing tweet {i+1}/{len(tweet_elements)}"
                            tweet_data = self.extract_tweet_data(tweet)

                            if tweet_data:
                                # Check if this tweet is already in our collection
                                is_duplicate = False
                                for existing_tweet in self.tweets:
                                    if existing_tweet.get('id') == tweet_data.get('id'):
                                        is_duplicate = True
                                        break

                                if not is_duplicate:
                                    logger.debug(f"Adding new tweet with ID: {tweet_data.get('id')}")
                                    self.tweets.append(tweet_data)
                                    tweets_found += 1
                                    # Update records count in real-time
                                    scrape_stats['records'] = tweets_found
                                    logger.info(f"Scraped {tweets_found}/{max_tweets} tweets")

                                    # Check if we've reached max tweets
                                    if tweets_found >= max_tweets:
                                        logger.info(f"Reached maximum number of tweets ({max_tweets}), will stop after this iteration")
                                        # Update progress to 100%
                                        scrape_stats['progress'] = 100
                                        # Force status to completed to stop the scraper
                                        scrape_stats['status'] = 'completed'
                                        # Set end time
                                        scrape_stats['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                        # Update records count before returning
                                        scrape_stats['records'] = len(self.tweets)
                                        # Break out of the loop
                                        return self.tweets

                                    # Check if we should stop after each tweet
                                    if should_stop_callback and should_stop_callback():
                                        logger.info("Stopping scraping process due to external request")
                                        break

                                    # Check if we're paused - if so, wait until resumed
                                    while scrape_stats.get('status') == 'paused':
                                        logger.debug("Scraping paused during tweet processing, waiting...")
                                        time.sleep(1)  # Wait 1 second before checking again
                                else:
                                    logger.debug(f"Skipping duplicate tweet with ID: {tweet_data.get('id')}")
                            else:
                                logger.debug("Extracted tweet data was None, skipping")

                        except Exception as e:
                            logger.warning(f"Error extracting tweet data: {e}", exc_info=True)
                            self.failed_requests += 1
                            continue

                    # Scroll with dynamic delay
                    logger.debug("Scrolling down the page")
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

                    # Take a screenshot after scrolling for debugging
                    if scroll_attempts % 5 == 0:  # Every 5 scrolls
                        try:
                            scroll_screenshot_path = f"debug_scroll_{scroll_attempts}.png"
                            self.driver.save_screenshot(scroll_screenshot_path)
                            logger.debug(f"Saved scroll screenshot to {scroll_screenshot_path}")
                        except Exception as ss_error:
                            logger.warning(f"Could not save scroll screenshot: {ss_error}")

                    logger.debug("Applying rate limiting")
                    self.apply_rate_limiting()
                    scroll_attempts += 1
                    logger.debug(f"Completed scroll attempt {scroll_attempts}")

                except TimeoutException:
                    logger.warning("Timeout while waiting for tweets to load", exc_info=True)
                    self.failed_requests += 1
                    if scroll_attempts >= 3:  # If multiple timeouts occur
                        logger.error("Multiple timeouts occurred, breaking loop")
                        break

                except Exception as e:
                    logger.error(f"Error during scraping: {e}", exc_info=True)
                    self.failed_requests += 1
                    break

            # Update status to completed
            if scrape_stats.get('status') != 'stopped':
                scrape_stats['status'] = 'completed'

            # Update records count
            scrape_stats['records'] = len(self.tweets)

            # Set end time
            scrape_stats['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Try to save data to CSV if we have tweets
            try:
                if len(self.tweets) > 0:
                    logger.info("Saving tweets to CSV")
                    # Import pandas here to avoid circular imports
                    import pandas as pd

                    # Get the CSV path from scrape_stats or use a default
                    csv_path = scrape_stats.get('csv_path', 'data/twitter_data.csv')

                    # Ensure the directory exists
                    import os
                    os.makedirs(os.path.dirname(csv_path), exist_ok=True)

                    # Create DataFrame and save to CSV
                    df = pd.DataFrame(self.tweets)
                    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    logger.info(f"Successfully saved {len(self.tweets)} tweets to CSV at {csv_path}")
            except Exception as e:
                logger.error(f"Error saving to CSV: {e}", exc_info=True)
                scrape_stats['errors'] = scrape_stats.get('errors', 0) + 1

            logger.info(f"Scraping completed. Found {len(self.tweets)} tweets in {scroll_attempts} scroll attempts")
            return self.tweets

        except Exception as e:
            logger.error(f"Critical error during scraping: {e}", exc_info=True)
            return []

        finally:
            logger.debug("Closing WebDriver")
            self.close_driver()
            self.close_driver()