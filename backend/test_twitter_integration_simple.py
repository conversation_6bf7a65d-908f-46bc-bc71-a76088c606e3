#!/usr/bin/env python3
"""
Simple Twitter Integration Test

This script performs basic validation of the Twitter integration without requiring
actual network connections or browser automation.
"""

import os
import sys

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        # Test enhanced scraper import
        from actor.scrapers.twitter_scraper import TwitterScraper
        print("✅ TwitterScraper imported successfully")
        
        # Test engine import
        from actor.engines.twitter_engine import TwitterEngine
        print("✅ TwitterEngine imported successfully")
        
        # Test authenticator import
        from actor.utils.twitter_auth import TwitterAuthenticator
        print("✅ TwitterAuthenticator imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {str(e)}")
        return False

def test_scraper_initialization():
    """Test that the enhanced scraper can be initialized."""
    print("\n🔧 Testing scraper initialization...")
    
    try:
        from actor.scrapers.twitter_scraper import TwitterScraper
        
        # Test initialization with credentials
        scraper = TwitterScraper(
            email="<EMAIL>",
            username="testuser",
            password="testpass",
            max_retries=2,
            delay_between_scrolls=2,
            rate_limit_delay=1
        )
        
        print("✅ Enhanced scraper initialized successfully")
        
        # Test that required attributes are set
        assert scraper.email == "<EMAIL>"
        assert scraper.username == "testuser"
        assert scraper.password == "testpass"
        assert scraper.max_retries == 2
        
        print("✅ Scraper attributes set correctly")
        
        # Test that methods exist
        assert hasattr(scraper, 'search_tweets')
        assert hasattr(scraper, 'login_to_twitter')
        assert hasattr(scraper, 'setup_driver')
        assert hasattr(scraper, 'extract_tweet_data')
        assert hasattr(scraper, 'start_scraping')
        
        print("✅ Required methods exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraper initialization failed: {str(e)}")
        return False

def test_engine_initialization():
    """Test that the Twitter engine can be initialized."""
    print("\n🚀 Testing engine initialization...")
    
    try:
        from actor.engines.twitter_engine import TwitterEngine
        
        # Test initialization
        engine = TwitterEngine()
        
        print("✅ TwitterEngine initialized successfully")
        
        # Test that required methods exist
        assert hasattr(engine, 'search_content')
        assert hasattr(engine, 'scrape_user_content')
        assert hasattr(engine, 'scrape_my_content')
        assert hasattr(engine, 'scrape_feed')
        assert hasattr(engine, 'authenticate')
        assert hasattr(engine, 'normalize_data')
        
        print("✅ Required engine methods exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Engine initialization failed: {str(e)}")
        return False

def test_authenticator_initialization():
    """Test that the Twitter authenticator can be initialized."""
    print("\n🔐 Testing authenticator initialization...")
    
    try:
        from actor.utils.twitter_auth import TwitterAuthenticator
        
        # Test initialization
        authenticator = TwitterAuthenticator()
        
        print("✅ TwitterAuthenticator initialized successfully")
        
        # Test that required methods exist
        assert hasattr(authenticator, 'authenticate')
        assert hasattr(authenticator, 'login')
        assert hasattr(authenticator, 'verify_credentials')
        assert hasattr(authenticator, 'create_authenticated_scraper')
        
        print("✅ Required authenticator methods exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Authenticator initialization failed: {str(e)}")
        return False

def test_task_functions():
    """Test that task helper functions exist."""
    print("\n📋 Testing task functions...")
    
    try:
        from actor.tasks import update_twitter_task_progress, handle_twitter_task_error
        
        print("✅ Task helper functions imported successfully")
        
        # Test that functions are callable
        assert callable(update_twitter_task_progress)
        assert callable(handle_twitter_task_error)
        
        print("✅ Task helper functions are callable")
        
        return True
        
    except Exception as e:
        print(f"❌ Task functions test failed: {str(e)}")
        return False

def test_integration_compatibility():
    """Test that the components work together."""
    print("\n🔗 Testing integration compatibility...")
    
    try:
        from actor.scrapers.twitter_scraper import TwitterScraper
        from actor.engines.twitter_engine import TwitterEngine
        from actor.utils.twitter_auth import TwitterAuthenticator
        
        # Test that authenticator can create scraper
        authenticator = TwitterAuthenticator()
        
        # This should not fail even without actual credentials
        try:
            scraper = authenticator.create_authenticated_scraper(
                username="test",
                password="test",
                email="<EMAIL>"
            )
            print("✅ Authenticator can create scraper instances")
        except Exception as e:
            # This is expected without proper setup, but the method should exist
            if "create_authenticated_scraper" in str(e):
                print("❌ create_authenticated_scraper method issue")
                return False
            else:
                print("✅ Authenticator integration works (expected error for test credentials)")
        
        # Test that engine uses enhanced scraper format
        engine = TwitterEngine()
        
        # Test normalize_data with enhanced scraper format
        test_tweet = {
            'id': '12345',
            'text': 'Test tweet',
            'author_username': 'testuser',
            'author_name': 'Test User',
            'date': '2024-01-01T00:00:00Z',
            'likes': 10,
            'retweets': 5,
            'replies': 2,
            'has_media': False,
            'media_count': 0,
            'author_verified': False
        }
        
        normalized = engine.normalize_data(test_tweet, 'tweet')
        
        # Check that normalization works with enhanced format
        assert normalized['platform'] == 'twitter'
        assert normalized['author'] == 'testuser'
        assert normalized['content'] == 'Test tweet'
        
        print("✅ Engine properly handles enhanced scraper data format")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration compatibility test failed: {str(e)}")
        return False

def main():
    """Run all validation tests."""
    print("🧪 TWITTER INTEGRATION VALIDATION TESTS")
    print("=" * 60)
    
    test_results = []
    
    # Run tests
    test_results.append(("Import Test", test_imports()))
    test_results.append(("Scraper Initialization", test_scraper_initialization()))
    test_results.append(("Engine Initialization", test_engine_initialization()))
    test_results.append(("Authenticator Initialization", test_authenticator_initialization()))
    test_results.append(("Task Functions", test_task_functions()))
    test_results.append(("Integration Compatibility", test_integration_compatibility()))
    
    # Summary
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} validation tests passed")
    
    if passed == total:
        print("\n🎉 All validation tests passed!")
        print("✅ Twitter integration is properly configured and ready for use.")
        print("\n📝 Next steps:")
        print("   1. Install required dependencies (selenium, chromedriver-autoinstaller)")
        print("   2. Set up valid Twitter credentials in ActorAccount")
        print("   3. Run actual scraping tests with real credentials")
        return True
    else:
        print("\n⚠️  Some validation tests failed.")
        print("❌ Please fix the issues before proceeding with actual scraping.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
