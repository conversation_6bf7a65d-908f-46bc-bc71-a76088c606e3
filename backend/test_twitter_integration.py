#!/usr/bin/env python3
"""
Test script for Twitter Engine Integration

This script tests the enhanced Twitter scraper integration with the actor system.
It verifies that the enhanced scraper works properly with authentication and data extraction.

Usage:
    python test_twitter_integration.py

Note: This script requires valid Twitter credentials to be set in the test configuration.
"""

import os
import sys
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fullstax.settings')
django.setup()

from actor.scrapers.twitter_scraper import TwitterScraper
from actor.engines.twitter_engine import Twitter<PERSON><PERSON><PERSON>
from actor.utils.twitter_auth import TwitterAuthenticator
from actor.models import ActorAccount
from django.contrib.auth.models import User

def test_enhanced_scraper():
    """Test the enhanced Twitter scraper directly."""
    print("=" * 60)
    print("TESTING ENHANCED TWITTER SCRAPER")
    print("=" * 60)
    
    # Test credentials from memory
    test_email = "<EMAIL>"
    test_username = "putrayol"
    test_password = "Puyol@102410"
    
    try:
        print(f"🔧 Initializing enhanced scraper for @{test_username}...")
        scraper = TwitterScraper(
            email=test_email,
            username=test_username,
            password=test_password,
            max_retries=2,
            delay_between_scrolls=2,
            rate_limit_delay=1
        )
        
        print("✅ Enhanced scraper initialized successfully")
        
        # Test search functionality
        print(f"🔍 Testing search for 'Indonesia news'...")
        result = scraper.search_tweets(
            query="Indonesia news",
            count=5
        )
        
        if result.get('success'):
            tweets = result.get('tweets', [])
            print(f"✅ Search successful! Found {len(tweets)} tweets")
            
            # Display first tweet details
            if tweets:
                first_tweet = tweets[0]
                print(f"📝 First tweet preview:")
                print(f"   ID: {first_tweet.get('id', 'N/A')}")
                print(f"   Author: @{first_tweet.get('author_username', 'N/A')}")
                print(f"   Text: {first_tweet.get('text', 'N/A')[:100]}...")
                print(f"   Date: {first_tweet.get('date', 'N/A')}")
                print(f"   Likes: {first_tweet.get('likes', 0)}")
                print(f"   Retweets: {first_tweet.get('retweets', 0)}")
        else:
            print(f"❌ Search failed: {result.get('error', 'Unknown error')}")
            return False
            
        # Close scraper
        scraper.close()
        print("🔒 Scraper closed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced scraper test failed: {str(e)}")
        return False

def test_twitter_authenticator():
    """Test the Twitter authenticator."""
    print("\n" + "=" * 60)
    print("TESTING TWITTER AUTHENTICATOR")
    print("=" * 60)
    
    # Test credentials
    test_email = "<EMAIL>"
    test_username = "putrayol"
    test_password = "Puyol@102410"
    
    try:
        print(f"🔐 Testing authentication for @{test_username}...")
        authenticator = TwitterAuthenticator()
        
        # Test credential verification
        result = authenticator.verify_credentials(
            username=test_username,
            password=test_password,
            email=test_email
        )
        
        if result.get('success'):
            print("✅ Authentication test successful!")
            print(f"   Message: {result.get('message', 'N/A')}")
            return True
        else:
            print(f"❌ Authentication failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Authenticator test failed: {str(e)}")
        return False

def test_twitter_engine():
    """Test the Twitter engine with a mock account."""
    print("\n" + "=" * 60)
    print("TESTING TWITTER ENGINE")
    print("=" * 60)
    
    try:
        # Create or get test user
        test_user, created = User.objects.get_or_create(
            username='test_twitter_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        
        if created:
            print("👤 Created test user")
        else:
            print("👤 Using existing test user")
        
        # Create or get test Twitter account
        test_account, created = ActorAccount.objects.get_or_create(
            user=test_user,
            platform='twitter',
            platform_username='putrayol',
            defaults={
                'email': '<EMAIL>',
                'password': 'Puyol@102410',  # This will be encrypted
                'is_active': True
            }
        )
        
        if created:
            print("📱 Created test Twitter account")
        else:
            print("📱 Using existing test Twitter account")
        
        # Test Twitter engine
        print("🚀 Initializing Twitter engine...")
        engine = TwitterEngine()
        
        # Test search content
        print("🔍 Testing content search...")
        results = engine.search_content(
            account=test_account,
            keywords=['Indonesia', 'news'],
            limit=3
        )
        
        print(f"✅ Search completed! Found {len(results)} results")
        
        # Display results
        for i, result in enumerate(results[:2]):  # Show first 2 results
            print(f"📄 Result {i+1}:")
            print(f"   Platform: {result.get('platform', 'N/A')}")
            print(f"   Author: {result.get('author', 'N/A')}")
            print(f"   Content: {result.get('content', 'N/A')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Twitter engine test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🧪 TWITTER ENGINE INTEGRATION TESTS")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = []
    
    # Run tests
    test_results.append(("Enhanced Scraper", test_enhanced_scraper()))
    test_results.append(("Twitter Authenticator", test_twitter_authenticator()))
    test_results.append(("Twitter Engine", test_twitter_engine()))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Twitter integration is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
