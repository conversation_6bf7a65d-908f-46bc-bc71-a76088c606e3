from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from ..utils.anti_detection import get_random_user_agent, random_delay, simulate_human_interaction
from ..utils.config import LOAD_TIMEOUT, SCROLL_PAUSE_TIME
from .driver_setup import create_driver, return_driver
from .data_processor import parse_search_results
from ..exceptions import TikTokScrapingException, TikTokBlockedException, TikTokLoginRequiredException
from django.core.cache import cache
import time
from tenacity import retry, stop_after_attempt, wait_exponential

def get_cached_search_results(keyword):
    cache_key = f'tiktok_search_{keyword}'
    return cache.get(cache_key)

def cache_search_results(keyword, results):
    cache_key = f'tiktok_search_{keyword}'
    cache.set(cache_key, results, timeout=3600)  # Cache for 1 hour

def _apply_session_data(driver, session_data):
    """Apply session data (cookies, tokens) to the WebDriver."""
    try:
        # Navigate to TikTok first to set domain for cookies
        driver.get("https://www.tiktok.com")
        
        # Apply cookies if available
        cookies = session_data.get('cookies', [])
        for cookie in cookies:
            try:
                driver.add_cookie(cookie)
            except Exception as e:
                print(f"Failed to add cookie: {e}")
        
        # Apply tokens via localStorage if available
        tokens = session_data.get('tokens', {})
        if tokens:
            for key, value in tokens.items():
                try:
                    driver.execute_script(f"localStorage.setItem('{key}', '{value}');")
                except Exception as e:
                    print(f"Failed to set localStorage item {key}: {e}")
        
        # Refresh page to apply session data
        driver.refresh()
        time.sleep(2)
        
    except Exception as e:
        print(f"Error applying session data: {e}")

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def scrape_tiktok_search(keyword, proxy=None, max_results=50, session_data=None):
    # Check cache first
    cached_results = get_cached_search_results(keyword)
    if cached_results:
        return cached_results[:max_results]

    driver = None
    try:
        user_agent = get_random_user_agent()
        driver = create_driver(
            headless=True,
            proxy=proxy,
            user_agent=user_agent
        )
        
        # Apply session data if available (cookies, tokens, etc.)
        if session_data and session_data.get('authenticated'):
            _apply_session_data(driver, session_data)
        
        # Navigate to search URL
        search_url = f"https://www.tiktok.com/search?q={keyword}"
        driver.get(search_url)
        
        # Wait for content to load using XPath
        try:
            WebDriverWait(driver, LOAD_TIMEOUT).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'tiktok-1s72ajp-DivVideoContainer')]"))
            )
        except TimeoutException:
            raise TikTokScrapingException("Search results failed to load within timeout")
        
        # Detect login walls
        if "login" in driver.current_url.lower():
            raise TikTokLoginRequiredException("Login required to access search results")
        
        # Detect CAPTCHAs
        if "captcha" in driver.page_source.lower():
            raise TikTokBlockedException("CAPTCHA detected during search")
        
        # Simulate human behavior
        simulate_human_interaction(driver)
        
        # Scroll to load more results
        results = []
        last_height = driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_scroll_attempts = 10
        
        while len(results) < max_results and scroll_attempts < max_scroll_attempts:
            # Scroll down
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(SCROLL_PAUSE_TIME)
            
            # Parse current results
            page_source = driver.page_source
            current_results = parse_search_results(page_source)
            
            # Deduplicate results
            new_results = [r for r in current_results if r not in results]
            results.extend(new_results)
            
            # Check if we've reached the end
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                scroll_attempts += 1
            else:
                scroll_attempts = 0
            last_height = new_height
            
            # Random delay between scrolls
            random_delay(1, 3)
            
            # Break if we have enough results
            if len(results) >= max_results:
                results = results[:max_results]
                break
        
        if not results:
            raise TikTokScrapingException(f"No results found for keyword: {keyword}")
        
        # Cache the results before returning
        cache_search_results(keyword, results)
        return results
    
    except Exception as e:
        if isinstance(e, (TikTokScrapingException, TikTokBlockedException, TikTokLoginRequiredException)):
            raise e
        raise TikTokScrapingException(f"Search scraping failed: {str(e)}")
    finally:
        if driver:
            return_driver(driver)