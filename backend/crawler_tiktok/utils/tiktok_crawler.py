import logging
import time
import urllib.parse
from typing import Dict, List, Optional, Tuple
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException
)

from .anti_detection import simulate_human_interaction, random_delay
from .data_processor import parse_tiktok_data

logger = logging.getLogger(__name__)

class TikTokCrawler:
    def __init__(self, driver, timeout: int = 20):
        self.driver = driver
        self.timeout = timeout
        self.wait = WebDriverWait(driver, timeout)

    def _handle_popups(self) -> None:
        """Handle various TikTok popups that might interfere with scraping."""
        try:
            # Cookie consent popup - using XPath selectors
            cookie_xpath_selectors = [
                "//button[@data-e2e='cookie-banner-accept']",
                "//button[contains(@class, 'accept-cookie')]",
                "//button[contains(text(), 'Accept all')]"
            ]
            for selector in cookie_xpath_selectors:
                try:
                    cookie_btn = self.driver.find_element(By.XPATH, selector)
                    cookie_btn.click()
                    random_delay(0.5, 1.0)
                    break
                except (NoSuchElementException, ElementClickInterceptedException):
                    continue

            # Login popup - using XPath selectors
            login_popup_xpath_selectors = [
                "//div[@data-e2e='modal-close-inner-button']",
                "//div[contains(@class, 'modal-close')]"
            ]
            for selector in login_popup_xpath_selectors:
                try:
                    close_btn = self.driver.find_element(By.XPATH, selector)
                    close_btn.click()
                    random_delay(0.5, 1.0)
                    break
                except (NoSuchElementException, ElementClickInterceptedException):
                    continue

        except Exception as e:
            logger.warning(f"Error handling popups: {str(e)}")

    def _check_content_availability(self) -> Tuple[bool, str]:
        """Check if content is available or if there are blocking conditions."""
        try:
            # Check for login wall - using XPath selectors
            login_xpath_indicators = [
                "//div[contains(text(), 'Sign up for TikTok')]",
                "//div[contains(text(), 'Log in to TikTok')]",
                "//button[@data-e2e='login-button']"
            ]
            for indicator in login_xpath_indicators:
                if len(self.driver.find_elements(By.XPATH, indicator)) > 0:
                    return False, "Login required"

            # Check for CAPTCHA - using XPath selectors
            captcha_xpath_indicators = [
                "//div[contains(text(), 'Verify you are human')]",
                "//div[contains(@class, 'captcha_verify')]",
                "//iframe[contains(@title, 'Captcha')]"
            ]
            for indicator in captcha_xpath_indicators:
                if len(self.driver.find_elements(By.XPATH, indicator)) > 0:
                    return False, "CAPTCHA detected"

            # Check for removed content
            removed_indicators = [
                "//div[contains(text(), 'Video currently unavailable')]",
                "//div[contains(text(), 'This video has been removed')]",
                "//div[contains(text(), 'This account is private')]"
            ]
            for indicator in removed_indicators:
                if len(self.driver.find_elements(By.XPATH, indicator)) > 0:
                    return False, "Content unavailable"

            return True, "Content available"

        except Exception as e:
            logger.error(f"Error checking content availability: {str(e)}")
            return False, f"Error: {str(e)}"

    def _wait_for_content(self, content_selector: str, timeout: int = 15) -> bool:
        """Wait for content to load with multiple retry attempts and alternative selectors."""
        try:
            # First wait for page to load
            WebDriverWait(self.driver, 5).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Convert CSS selector to XPath if needed
            if content_selector.startswith('//') or content_selector.startswith('./'):
                # Already XPath
                xpath_selector = content_selector
            else:
                # Convert CSS to XPath
                if content_selector.startswith('div[data-e2e="browse-video"]'):
                    xpath_selector = "//div[@data-e2e='browse-video']"
                elif content_selector.startswith('div[data-e2e="user-post-item"]'):
                    xpath_selector = "//div[@data-e2e='user-post-item']"
                elif content_selector.startswith('*[data-e2e="search-card-desc"]'):
                    xpath_selector = "//*[@data-e2e='search-card-desc']"
                else:
                    # Generic conversion for data-e2e attributes
                    if '[data-e2e=' in content_selector:
                        attr_value = content_selector.split('[data-e2e=')[1].split(']')[0].strip('"\'')
                        xpath_selector = f"//*[@data-e2e='{attr_value}']"
                    else:
                        xpath_selector = f"//{content_selector}"

            # Then wait for specific content using XPath
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath_selector))
            )
            return True
        except TimeoutException:
            # Try alternative XPath selectors if main one fails
            alternative_xpath_selectors = [
                "//*[contains(@data-e2e, 'search')]",
                "//video",
                "//*[@data-e2e]"
            ]

            for alt_selector in alternative_xpath_selectors:
                try:
                    WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, alt_selector))
                    )
                    return True
                except TimeoutException:
                    continue
                    
            logger.error("Content failed to load after trying all selectors")
            return False
        except Exception as e:
            logger.error(f"Error waiting for content: {str(e)}")
            return False

    def scrape_video(self, video_url: str) -> Optional[Dict]:
        """Scrape data from a TikTok video page."""
        try:
            self.driver.get(video_url)
            random_delay(2, 4)

            # Handle initial popups
            self._handle_popups()

            # Check content availability
            available, message = self._check_content_availability()
            if not available:
                logger.warning(f"Video not available: {message}")
                return None

            # Wait for video content to load
            if not self._wait_for_content('div[data-e2e="browse-video"]'):
                return None

            # Simulate human behavior
            simulate_human_interaction(self.driver)

            # Extract video data
            return parse_tiktok_data(self.driver.page_source, 'video')

        except Exception as e:
            logger.error(f"Error scraping video: {str(e)}")
            return None

    def scrape_user(self, username: str) -> Optional[Dict]:
        """Scrape data from a TikTok user profile."""
        try:
            profile_url = f"https://www.tiktok.com/@{username}"
            self.driver.get(profile_url)
            random_delay(2, 4)

            # Handle initial popups
            self._handle_popups()

            # Check content availability
            available, message = self._check_content_availability()
            if not available:
                logger.warning(f"Profile not available: {message}")
                return None

            # Wait for profile content to load
            if not self._wait_for_content('div[data-e2e="user-post-item"]'):
                return None

            # Simulate human behavior
            simulate_human_interaction(self.driver)

            # Extract profile data
            return parse_tiktok_data(self.driver.page_source, 'user')

        except Exception as e:
            logger.error(f"Error scraping user profile: {str(e)}")
            return None

    def search_tiktok(self, keyword: str, max_results: int = 50) -> List[Dict]:
        """Search TikTok for videos matching a keyword."""
        try:
            encoded_keyword = urllib.parse.quote(keyword)
            search_url = f"https://www.tiktok.com/search?q={encoded_keyword}"
            self.driver.get(search_url)
            random_delay(2, 4)

            # Handle initial popups
            self._handle_popups()

            # Check content availability
            available, message = self._check_content_availability()
            if not available:
                logger.warning(f"Search not available: {message}")
                return []

            # Wait for search results to load - use any search card element
            if not self._wait_for_content('*[data-e2e="search-card-desc"]'):
                return []

            results = []
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            # Add a maximum scroll limit to prevent infinite loops
            max_scroll_attempts = 10
            scroll_count = 0
            
            # Add a timeout for the entire scraping process
            start_time = time.time()
            max_scrape_time = 60  # 60 seconds max for the entire scraping process

            while len(results) < max_results and scroll_count < max_scroll_attempts and (time.time() - start_time) < max_scrape_time:
                # Simulate human behavior
                simulate_human_interaction(self.driver)

                # Extract current results
                page_data = parse_tiktok_data(self.driver.page_source, 'search')
                if page_data:
                    # Check if page_data is a list before extending
                    if isinstance(page_data, list):
                        # Filter out duplicates based on some identifier
                        new_items = [item for item in page_data if not any(existing.get('id') == item.get('id') for existing in results)]
                        results.extend(new_items)
                        
                        # If no new items were added, we might be in a loop
                        if not new_items:
                            logger.warning("No new items found after scroll, possible end of results")
                            scroll_count += 1  # Count this as a failed scroll attempt
                        
                        if len(results) >= max_results:
                            results = results[:max_results]
                            break
                    else:
                        logger.warning(f"Unexpected page_data format: {type(page_data)}")

                # Scroll to load more results
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                random_delay(1, 2)
                scroll_count += 1

                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    logger.info("Reached end of page, no more results to load")
                    break
                last_height = new_height

            # Log completion reason
            if len(results) >= max_results:
                logger.info(f"Search completed: Reached max results limit ({max_results})")
            elif scroll_count >= max_scroll_attempts:
                logger.info(f"Search completed: Reached max scroll attempts ({max_scroll_attempts})")
            elif (time.time() - start_time) >= max_scrape_time:
                logger.info(f"Search completed: Reached max scrape time ({max_scrape_time}s)")

            return results

        except Exception as e:
            logger.error(f"Error performing search: {str(e)}")
            return []