{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://lf16-tiktok-common.ibytedtos.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-common-sign-useastred.tiktokcdn-eu.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400922454435602", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://web-va.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://us.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://login-no1a.www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://libraweb-sg.tiktok.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400922471787331", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-pu-sign-useast8.tiktokcdn-us.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-sg.tiktokcdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400922446027760", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 119896}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p16-sign-sg.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p77-sg.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://p77-sign-va.tiktokcdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://sf16-website-login.neutral.ttwstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://v16-webapp-prime.tiktok.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400922460604924", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 70414}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400922460669894", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "network_stats": {"srtt": 76266}, "server": "https://storage.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://www.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://webcast.tiktok.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mon.tiktokv.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL3Rpa3Rvay5jb20AAA==", false, 0], "server": "https://mcs-sg.tiktokv.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400922421004607", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 98498}, "server": "https://www.google.com", "supports_spdy": true}], "supports_quic": {"address": "2404:c0:7021:5106:4dd3:7098:ff6e:bba2", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}