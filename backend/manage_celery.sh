#!/bin/bash
"""
Celery Management Script

This script provides easy commands to manage Celery workers and tasks.

Usage:
    ./manage_celery.sh [command]

Commands:
    start       Start Celery worker
    stop        Stop Celery worker
    restart     Restart Celery worker
    status      Show Celery status
    cleanup     Clean up orphaned tasks
    clear       Clear Redis queues
    logs        Show Celery logs
    monitor     Start Celery monitoring (flower)
"""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CELERY_APP="backend"
VENV_PATH="venv/bin/activate"
LOG_LEVEL="info"
CONCURRENCY=4

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if virtual environment exists
check_venv() {
    if [ ! -f "$VENV_PATH" ]; then
        print_error "Virtual environment not found at $VENV_PATH"
        exit 1
    fi
}

# Check if Redis is running
check_redis() {
    if ! redis-cli ping > /dev/null 2>&1; then
        print_error "Redis is not running. Please start Redis first."
        exit 1
    fi
}

# Start Celery worker
start_celery() {
    print_status "Starting Celery worker..."
    check_venv
    check_redis
    
    # Check if already running
    if pgrep -f "celery.*worker" > /dev/null; then
        print_warning "Celery worker is already running"
        return
    fi
    
    source $VENV_PATH
    celery -A $CELERY_APP worker --loglevel=$LOG_LEVEL --concurrency=$CONCURRENCY &
    
    sleep 3
    if pgrep -f "celery.*worker" > /dev/null; then
        print_success "Celery worker started successfully"
    else
        print_error "Failed to start Celery worker"
    fi
}

# Stop Celery worker
stop_celery() {
    print_status "Stopping Celery worker..."
    
    if ! pgrep -f "celery.*worker" > /dev/null; then
        print_warning "Celery worker is not running"
        return
    fi
    
    # Graceful shutdown
    pkill -TERM -f "celery.*worker"
    
    # Wait for graceful shutdown
    sleep 5
    
    # Force kill if still running
    if pgrep -f "celery.*worker" > /dev/null; then
        print_warning "Forcing Celery worker shutdown..."
        pkill -KILL -f "celery.*worker"
    fi
    
    print_success "Celery worker stopped"
}

# Restart Celery worker
restart_celery() {
    print_status "Restarting Celery worker..."
    stop_celery
    sleep 2
    start_celery
}

# Show Celery status
show_status() {
    print_status "Checking Celery status..."
    
    # Check if worker is running
    if pgrep -f "celery.*worker" > /dev/null; then
        print_success "Celery worker is running"
        echo "Worker PIDs: $(pgrep -f 'celery.*worker' | tr '\n' ' ')"
    else
        print_warning "Celery worker is not running"
    fi
    
    # Check Redis
    if redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is running"
    else
        print_error "Redis is not running"
    fi
    
    # Show task statistics
    if [ -f "$VENV_PATH" ]; then
        source $VENV_PATH
        python cleanup_celery_tasks.py --stats
    fi
}

# Clean up orphaned tasks
cleanup_tasks() {
    print_status "Cleaning up orphaned tasks..."
    check_venv
    
    source $VENV_PATH
    python cleanup_celery_tasks.py
}

# Clear Redis queues
clear_queues() {
    print_status "Clearing Redis queues..."
    check_redis
    
    source $VENV_PATH
    python cleanup_celery_tasks.py --clear-redis
}

# Show Celery logs
show_logs() {
    print_status "Showing Celery logs..."
    
    if pgrep -f "celery.*worker" > /dev/null; then
        # Follow logs if worker is running
        tail -f /tmp/celery.log 2>/dev/null || echo "No log file found. Logs are shown in terminal."
    else
        print_warning "Celery worker is not running"
    fi
}

# Start Celery monitoring (Flower)
start_monitor() {
    print_status "Starting Celery monitoring (Flower)..."
    check_venv
    check_redis
    
    source $VENV_PATH
    
    # Check if flower is installed
    if ! python -c "import flower" 2>/dev/null; then
        print_error "Flower is not installed. Install with: pip install flower"
        exit 1
    fi
    
    celery -A $CELERY_APP flower --port=5555 &
    
    sleep 3
    print_success "Flower monitoring started at http://localhost:5555"
}

# Show help
show_help() {
    echo "Celery Management Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start       Start Celery worker"
    echo "  stop        Stop Celery worker"
    echo "  restart     Restart Celery worker"
    echo "  status      Show Celery status"
    echo "  cleanup     Clean up orphaned tasks"
    echo "  clear       Clear Redis queues"
    echo "  logs        Show Celery logs"
    echo "  monitor     Start Celery monitoring (flower)"
    echo "  help        Show this help message"
}

# Main script
case "$1" in
    start)
        start_celery
        ;;
    stop)
        stop_celery
        ;;
    restart)
        restart_celery
        ;;
    status)
        show_status
        ;;
    cleanup)
        cleanup_tasks
        ;;
    clear)
        clear_queues
        ;;
    logs)
        show_logs
        ;;
    monitor)
        start_monitor
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
