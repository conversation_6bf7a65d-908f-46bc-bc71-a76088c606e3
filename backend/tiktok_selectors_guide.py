#!/usr/bin/env python3
"""
TikTok Login Selectors Guide

Based on analysis of TikTok login pages and debug files, this guide provides
the most current and reliable XPath and CSS selectors for TikTok authentication.

Updated: January 2025
"""

# TikTok Login URLs
TIKTOK_LOGIN_URLS = {
    'main_login': 'https://www.tiktok.com/login',
    'email_login': 'https://www.tiktok.com/login/phone-or-email/email',
    'phone_login': 'https://www.tiktok.com/login/phone-or-email/phone',
    'qr_login': 'https://www.tiktok.com/login/qr',
    'signup': 'https://www.tiktok.com/signup'
}

# Primary Input Field Selectors
EMAIL_INPUT_SELECTORS = {
    'xpath': [
        "//input[@name='username']",
        "//input[@autocomplete='username']",
        "//input[@autocomplete='email']",
        "//input[@type='email']",
        "//input[contains(@placeholder, 'email')]",
        "//input[contains(@placeholder, 'Email')]",
        "//input[@data-e2e='email-input']",
        "//input[@data-e2e='login-email']",
        "//input[contains(@class, 'email')]",
        "//form//input[1]",  # First input in form (usually email)
    ],
    'css': [
        "input[name='username']",
        "input[autocomplete='username']",
        "input[autocomplete='email']",
        "input[type='email']",
        "input[placeholder*='email']",
        "input[placeholder*='Email']",
        "input[data-e2e='email-input']",
        "input[data-e2e='login-email']",
        "input[class*='email']",
        "form input:first-child",
    ]
}

PASSWORD_INPUT_SELECTORS = {
    'xpath': [
        "//input[@type='password']",
        "//input[@autocomplete='current-password']",
        "//input[@autocomplete='password']",
        "//input[@name='password']",
        "//input[contains(@placeholder, 'password')]",
        "//input[contains(@placeholder, 'Password')]",
        "//input[@data-e2e='password-input']",
        "//input[@data-e2e='login-password']",
        "//form//input[@type='password']",
        "//form//input[2]",  # Second input in form (usually password)
    ],
    'css': [
        "input[type='password']",
        "input[autocomplete='current-password']",
        "input[autocomplete='password']",
        "input[name='password']",
        "input[placeholder*='password']",
        "input[placeholder*='Password']",
        "input[data-e2e='password-input']",
        "input[data-e2e='login-password']",
        "form input[type='password']",
    ]
}

# Login Button Selectors
LOGIN_BUTTON_SELECTORS = {
    'xpath': [
        "//button[@type='submit']",
        "//button[contains(text(), 'Log in')]",
        "//button[contains(text(), 'Login')]",
        "//button[contains(text(), 'Sign in')]",
        "//button[@data-e2e='login-button']",
        "//button[@data-e2e='submit-button']",
        "//input[@type='submit']",
        "//form//button[contains(@class, 'submit')]",
        "//form//button[last()]",  # Last button in form (usually submit)
        "//div[contains(@class, 'login')]//button",
    ],
    'css': [
        "button[type='submit']",
        "button:contains('Log in')",
        "button:contains('Login')",
        "button:contains('Sign in')",
        "button[data-e2e='login-button']",
        "button[data-e2e='submit-button']",
        "input[type='submit']",
        "form button[class*='submit']",
        "div[class*='login'] button",
    ]
}

# Form Container Selectors
FORM_SELECTORS = {
    'xpath': [
        "//form",
        "//div[@data-e2e='login-form']",
        "//div[contains(@class, 'login-form')]",
        "//div[contains(@class, 'auth-form')]",
        "//div[contains(@class, 'signin-form')]",
        "//main//form",
        "//section//form",
    ],
    'css': [
        "form",
        "div[data-e2e='login-form']",
        "div[class*='login-form']",
        "div[class*='auth-form']",
        "div[class*='signin-form']",
        "main form",
        "section form",
    ]
}

# Error Message Selectors
ERROR_MESSAGE_SELECTORS = {
    'xpath': [
        "//*[contains(@class, 'error')]",
        "//*[@data-e2e='error-message']",
        "//*[@data-e2e='login-error']",
        "//*[contains(@class, 'alert')]",
        "//*[contains(@class, 'warning')]",
        "//*[@role='alert']",
        "//*[contains(text(), 'incorrect')]",
        "//*[contains(text(), 'invalid')]",
        "//*[contains(text(), 'error')]",
        "//div[contains(@style, 'color: red')]",
        "//span[contains(@style, 'color: red')]",
    ],
    'css': [
        "[class*='error']",
        "[data-e2e='error-message']",
        "[data-e2e='login-error']",
        "[class*='alert']",
        "[class*='warning']",
        "[role='alert']",
        "div[style*='color: red']",
        "span[style*='color: red']",
    ]
}

# CAPTCHA Detection Selectors
CAPTCHA_SELECTORS = {
    'xpath': [
        "//iframe[contains(@src, 'captcha')]",
        "//iframe[contains(@title, 'captcha')]",
        "//iframe[contains(@title, 'Captcha')]",
        "//*[contains(@class, 'captcha')]",
        "//*[@data-e2e='captcha']",
        "//*[contains(text(), 'verify')]",
        "//*[contains(text(), 'Verify')]",
        "//*[contains(text(), 'human')]",
        "//div[contains(@class, 'verification')]",
        "//div[contains(@class, 'challenge')]",
    ],
    'css': [
        "iframe[src*='captcha']",
        "iframe[title*='captcha']",
        "iframe[title*='Captcha']",
        "[class*='captcha']",
        "[data-e2e='captcha']",
        "div[class*='verification']",
        "div[class*='challenge']",
    ]
}

# Popup/Modal Close Selectors
POPUP_CLOSE_SELECTORS = {
    'xpath': [
        "//button[contains(@class, 'close')]",
        "//div[contains(@class, 'close')]",
        "//*[@data-e2e='modal-close']",
        "//*[@data-e2e='popup-close']",
        "//button[@aria-label='Close']",
        "//button[contains(@title, 'close')]",
        "//*[contains(@class, 'modal')]//button",
        "//*[contains(@class, 'overlay')]//button",
        "//button[text()='×']",
        "//span[text()='×']",
    ],
    'css': [
        "button[class*='close']",
        "div[class*='close']",
        "[data-e2e='modal-close']",
        "[data-e2e='popup-close']",
        "button[aria-label='Close']",
        "button[title*='close']",
        "[class*='modal'] button",
        "[class*='overlay'] button",
    ]
}

# Cookie Banner Selectors
COOKIE_BANNER_SELECTORS = {
    'xpath': [
        "//button[contains(text(), 'Accept')]",
        "//button[contains(text(), 'accept')]",
        "//button[contains(text(), 'Allow')]",
        "//button[contains(text(), 'OK')]",
        "//button[@data-e2e='cookie-accept']",
        "//button[@data-e2e='cookie-banner-accept']",
        "//*[contains(@class, 'cookie')]//button",
        "//*[contains(@class, 'consent')]//button",
    ],
    'css': [
        "button[data-e2e='cookie-accept']",
        "button[data-e2e='cookie-banner-accept']",
        "[class*='cookie'] button",
        "[class*='consent'] button",
    ]
}

# Navigation/Success Indicators
SUCCESS_INDICATORS = {
    'xpath': [
        "//*[@data-e2e='header-avatar']",
        "//*[@data-e2e='user-avatar']",
        "//nav",
        "//header",
        "//a[contains(@href, '/@')]",  # Profile link
        "//*[contains(@class, 'profile')]",
        "//*[contains(@class, 'dashboard')]",
        "//main[contains(@class, 'feed')]",
    ],
    'css': [
        "[data-e2e='header-avatar']",
        "[data-e2e='user-avatar']",
        "nav",
        "header",
        "a[href*='/@']",
        "[class*='profile']",
        "[class*='dashboard']",
        "main[class*='feed']",
    ]
}

# Two-Factor Authentication Selectors
TWO_FACTOR_SELECTORS = {
    'xpath': [
        "//input[@type='text'][contains(@placeholder, 'code')]",
        "//input[@type='text'][contains(@placeholder, 'Code')]",
        "//input[@data-e2e='verification-code']",
        "//input[@data-e2e='2fa-code']",
        "//input[contains(@placeholder, 'verification')]",
        "//input[contains(@placeholder, 'Verification')]",
        "//*[contains(text(), '2FA')]//following::input",
        "//*[contains(text(), 'verification')]//following::input",
    ],
    'css': [
        "input[type='text'][placeholder*='code']",
        "input[type='text'][placeholder*='Code']",
        "input[data-e2e='verification-code']",
        "input[data-e2e='2fa-code']",
        "input[placeholder*='verification']",
        "input[placeholder*='Verification']",
    ]
}

# Recommended Selector Priority Order
SELECTOR_PRIORITY = {
    'email_input': [
        "//input[@autocomplete='username']",
        "//input[@name='username']",
        "//input[@type='email']",
        "//input[contains(@placeholder, 'email')]",
        "//form//input[1]"
    ],
    'password_input': [
        "//input[@type='password']",
        "//input[@autocomplete='current-password']",
        "//input[@name='password']",
        "//form//input[@type='password']"
    ],
    'login_button': [
        "//button[@type='submit']",
        "//button[contains(text(), 'Log in')]",
        "//input[@type='submit']",
        "//form//button[last()]"
    ]
}

# Best Practices for TikTok Login Automation
BEST_PRACTICES = {
    'wait_times': {
        'page_load': 5,  # seconds
        'element_wait': 10,  # seconds
        'after_click': 2,  # seconds
        'after_input': 1,  # seconds
    },
    'retry_attempts': 3,
    'user_agents': [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ],
    'viewport_sizes': [
        (1920, 1080),
        (1366, 768),
        (1440, 900),
        (1536, 864)
    ]
}

# Common TikTok Login Flow Steps
LOGIN_FLOW_STEPS = [
    {
        'step': 1,
        'action': 'navigate_to_login',
        'url': TIKTOK_LOGIN_URLS['email_login'],
        'wait_for': FORM_SELECTORS['xpath'][0]
    },
    {
        'step': 2,
        'action': 'handle_popups',
        'selectors': POPUP_CLOSE_SELECTORS['xpath'] + COOKIE_BANNER_SELECTORS['xpath']
    },
    {
        'step': 3,
        'action': 'enter_email',
        'selectors': EMAIL_INPUT_SELECTORS['xpath'],
        'wait_after': 1
    },
    {
        'step': 4,
        'action': 'enter_password',
        'selectors': PASSWORD_INPUT_SELECTORS['xpath'],
        'wait_after': 1
    },
    {
        'step': 5,
        'action': 'click_login',
        'selectors': LOGIN_BUTTON_SELECTORS['xpath'],
        'wait_after': 3
    },
    {
        'step': 6,
        'action': 'check_success',
        'selectors': SUCCESS_INDICATORS['xpath'],
        'timeout': 15
    },
    {
        'step': 7,
        'action': 'handle_2fa',
        'selectors': TWO_FACTOR_SELECTORS['xpath'],
        'optional': True
    }
]

def get_selector_by_priority(selector_type, method='xpath'):
    """
    Get selectors by priority order
    
    Args:
        selector_type: Type of selector ('email_input', 'password_input', 'login_button')
        method: 'xpath' or 'css'
    
    Returns:
        List of selectors in priority order
    """
    if selector_type in SELECTOR_PRIORITY:
        return SELECTOR_PRIORITY[selector_type]
    
    # Fallback to full selector lists
    selector_map = {
        'email_input': EMAIL_INPUT_SELECTORS,
        'password_input': PASSWORD_INPUT_SELECTORS,
        'login_button': LOGIN_BUTTON_SELECTORS,
        'error_message': ERROR_MESSAGE_SELECTORS,
        'captcha': CAPTCHA_SELECTORS,
        'popup_close': POPUP_CLOSE_SELECTORS,
        'success_indicator': SUCCESS_INDICATORS
    }
    
    if selector_type in selector_map and method in selector_map[selector_type]:
        return selector_map[selector_type][method]
    
    return []

def print_selector_guide():
    """Print a formatted guide of all selectors"""
    print("=" * 80)
    print("TIKTOK LOGIN SELECTORS GUIDE")
    print("=" * 80)
    
    sections = [
        ("EMAIL INPUT SELECTORS", EMAIL_INPUT_SELECTORS),
        ("PASSWORD INPUT SELECTORS", PASSWORD_INPUT_SELECTORS),
        ("LOGIN BUTTON SELECTORS", LOGIN_BUTTON_SELECTORS),
        ("ERROR MESSAGE SELECTORS", ERROR_MESSAGE_SELECTORS),
        ("CAPTCHA SELECTORS", CAPTCHA_SELECTORS),
        ("POPUP CLOSE SELECTORS", POPUP_CLOSE_SELECTORS),
        ("SUCCESS INDICATORS", SUCCESS_INDICATORS)
    ]
    
    for section_name, selectors in sections:
        print(f"\n{section_name}:")
        print("-" * len(section_name))
        
        print("XPath Selectors:")
        for i, xpath in enumerate(selectors['xpath'][:5], 1):  # Top 5
            print(f"  {i}. {xpath}")
        
        print("CSS Selectors:")
        for i, css in enumerate(selectors['css'][:3], 1):  # Top 3
            print(f"  {i}. {css}")

if __name__ == "__main__":
    print_selector_guide()
    
    print("\n" + "=" * 80)
    print("RECOMMENDED USAGE:")
    print("=" * 80)
    
    print("\n1. Email Input (try in order):")
    for i, selector in enumerate(get_selector_by_priority('email_input'), 1):
        print(f"   {i}. {selector}")
    
    print("\n2. Password Input (try in order):")
    for i, selector in enumerate(get_selector_by_priority('password_input'), 1):
        print(f"   {i}. {selector}")
    
    print("\n3. Login Button (try in order):")
    for i, selector in enumerate(get_selector_by_priority('login_button'), 1):
        print(f"   {i}. {selector}")
    
    print(f"\n4. Best Practices:")
    print(f"   - Wait {BEST_PRACTICES['wait_times']['page_load']}s for page load")
    print(f"   - Wait {BEST_PRACTICES['wait_times']['element_wait']}s for elements")
    print(f"   - Retry up to {BEST_PRACTICES['retry_attempts']} times")
    print(f"   - Use random user agents and viewport sizes")
    
    print("\n" + "=" * 80)
