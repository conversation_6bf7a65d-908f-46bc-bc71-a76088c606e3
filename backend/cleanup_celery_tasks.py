#!/usr/bin/env python3
"""
Celery Task Cleanup Utility

This script cleans up orphaned Celery tasks that remain in "RUNNING" status
after worker shutdowns or crashes.

Usage:
    python cleanup_celery_tasks.py [--dry-run] [--hours=24]

Options:
    --dry-run    Show what would be cleaned up without making changes
    --hours=N    Clean up tasks older than N hours (default: 1)
"""

import os
import sys
import django
from datetime import datetime, timedelta
import argparse

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor.models import ActorTask
from django.utils import timezone

def cleanup_orphaned_tasks(hours_threshold=1, dry_run=False):
    """
    Clean up orphaned tasks that are stuck in RUNNING status.
    
    Args:
        hours_threshold (int): Tasks older than this many hours will be cleaned
        dry_run (bool): If True, only show what would be cleaned without making changes
    """
    print("=" * 60)
    print("CELERY TASK CLEANUP UTILITY")
    print("=" * 60)
    
    # Find orphaned running tasks
    cutoff_time = timezone.now() - timedelta(hours=hours_threshold)
    orphaned_tasks = ActorTask.objects.filter(
        status='RUNNING',
        started_at__lt=cutoff_time
    )
    
    print(f"🔍 Searching for orphaned tasks older than {hours_threshold} hours...")
    print(f"📅 Cutoff time: {cutoff_time}")
    print(f"📊 Found {orphaned_tasks.count()} orphaned tasks")
    
    if orphaned_tasks.count() == 0:
        print("✅ No orphaned tasks found!")
        return
    
    # Show details of orphaned tasks
    print("\n📋 Orphaned tasks details:")
    print("-" * 60)
    for task in orphaned_tasks:
        age = timezone.now() - task.started_at if task.started_at else timedelta(0)
        print(f"Task {task.id:3d}: {task.task_type:20s} | Age: {age} | Started: {task.started_at}")
    
    if dry_run:
        print("\n🔍 DRY RUN MODE - No changes will be made")
        print("Run without --dry-run to actually clean up these tasks")
        return
    
    # Confirm cleanup
    print(f"\n⚠️  About to mark {orphaned_tasks.count()} tasks as FAILED")
    response = input("Continue? (y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ Cleanup cancelled")
        return
    
    # Perform cleanup
    print("\n🧹 Cleaning up orphaned tasks...")
    cleaned_count = 0
    
    for task in orphaned_tasks:
        try:
            task.status = 'FAILED'
            task.error_message = f'Task was orphaned (cleanup at {timezone.now()})'
            task.completed_at = timezone.now()
            task.save()
            
            print(f"✅ Cleaned task {task.id}: {task.task_type}")
            cleaned_count += 1
            
        except Exception as e:
            print(f"❌ Failed to clean task {task.id}: {str(e)}")
    
    print(f"\n🎉 Cleanup completed! {cleaned_count} tasks cleaned up")

def show_task_statistics():
    """Show current task statistics."""
    print("\n📊 CURRENT TASK STATISTICS")
    print("-" * 60)
    
    # Count by status
    statuses = ['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED']
    for status in statuses:
        count = ActorTask.objects.filter(status=status).count()
        print(f"{status:12s}: {count:4d} tasks")
    
    # Recent tasks
    recent_tasks = ActorTask.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).count()
    print(f"{'Recent (24h)':12s}: {recent_tasks:4d} tasks")
    
    # Currently running tasks
    running_tasks = ActorTask.objects.filter(status='RUNNING')
    if running_tasks.exists():
        print(f"\n🏃 Currently running tasks:")
        for task in running_tasks:
            age = timezone.now() - task.started_at if task.started_at else timedelta(0)
            print(f"  Task {task.id}: {task.task_type} (running for {age})")

def clear_redis_queues():
    """Clear Redis queues (requires confirmation)."""
    print("\n🗑️  REDIS QUEUE CLEANUP")
    print("-" * 60)
    print("⚠️  This will clear ALL pending Celery tasks from Redis!")
    print("⚠️  Only do this if you're sure no important tasks are queued!")
    
    response = input("Clear Redis queues? (y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ Redis cleanup cancelled")
        return
    
    try:
        import subprocess
        result = subprocess.run(['redis-cli', 'flushall'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Redis queues cleared successfully")
        else:
            print(f"❌ Failed to clear Redis: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error clearing Redis: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description='Clean up orphaned Celery tasks')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be cleaned without making changes')
    parser.add_argument('--hours', type=int, default=1,
                       help='Clean tasks older than N hours (default: 1)')
    parser.add_argument('--stats', action='store_true',
                       help='Show task statistics only')
    parser.add_argument('--clear-redis', action='store_true',
                       help='Clear Redis queues (interactive)')
    
    args = parser.parse_args()
    
    if args.stats:
        show_task_statistics()
        return
    
    if args.clear_redis:
        clear_redis_queues()
        return
    
    # Main cleanup
    cleanup_orphaned_tasks(hours_threshold=args.hours, dry_run=args.dry_run)
    
    # Show statistics after cleanup
    show_task_statistics()

if __name__ == "__main__":
    main()
