"""
Twitter Engine for Actor Platform System

This module implements the Twitter-specific engine that handles authentication,
scraping, and data processing for Twitter platform.

Features:
- Content search by keywords
- User profile scraping
- Tweet collection
- Trend analysis
- Real-time data extraction
"""

from typing import Dict, List, Any, Optional
import logging
import json
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings

from .base_engine import BaseActorEngine, EngineRegistry
from ..utils.twitter_auth import TwitterAuthenticator
from ..scrapers.twitter_scraper import TwitterScraper
from ..models import ActorAccount

logger = logging.getLogger(__name__)


class TwitterEngine(BaseActorEngine):
    """Twitter implementation of the Actor engine with full functionality."""

    def __init__(self, platform: str = 'twitter'):
        super().__init__(platform)
        self.authenticator = TwitterAuthenticator()
        self.scraper = None
    
    def authenticate(self, account: ActorAccount, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Authenticate with Twitter using account credentials.

        Args:
            account: ActorAccount instance with Twitter credentials
            credentials: Additional authentication parameters

        Returns:
            Authentication result dictionary
        """
        try:
            self.logger.info(f"Starting Twitter authentication for @{account.platform_username}")

            # Get credentials
            username = account.platform_username
            password = account.get_decrypted_password()

            if not password:
                return {
                    'success': False,
                    'error': 'No password available for Twitter authentication'
                }

            # Real Twitter authentication using TwitterAuthenticator
            self.logger.info("Starting real Twitter authentication")

            # Use real Twitter authenticator - NO MOCK DATA
            auth_result = self.authenticator.login(username, password)

            if not auth_result.get('success'):
                return {
                    'success': False,
                    'error': f'Twitter authentication failed: {auth_result.get("error", "Unknown error")}'
                }

            # Save real session data from authenticator
            session_data = auth_result.get('session_data', {})
            if session_data:
                account.encrypt_session_data(session_data)
                account.last_login = timezone.now()
                account.session_expires_at = timezone.now() + timedelta(hours=24)
                account.save()

            return {
                'success': True,
                'message': f'Twitter authentication successful for @{username}',
                'session_data': session_data
            }

        except Exception as e:
            self.logger.error(f"Twitter authentication failed: {str(e)}")
            return {
                'success': False,
                'error': f'Authentication failed: {str(e)}'
            }
    
    def verify_session(self, account: ActorAccount) -> bool:
        """
        Verify if the Twitter session is still valid.

        Args:
            account: ActorAccount instance to verify

        Returns:
            True if session is valid, False otherwise
        """
        try:
            session_data = account.decrypt_session_data()

            if not session_data:
                return False

            # Check if session has expired
            expires_at = session_data.get('expires_at')
            if expires_at:
                expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00').replace('+00:00', ''))
                if datetime.now() > expiry_time:
                    self.logger.info(f"Twitter session expired for @{account.platform_username}")
                    return False

            # Check if required session data exists
            required_fields = ['authenticated', 'auth_token', 'username']
            for field in required_fields:
                if not session_data.get(field):
                    self.logger.warning(f"Missing required session field: {field}")
                    return False

            self.logger.info(f"Twitter session valid for @{account.platform_username}")
            return True

        except Exception as e:
            self.logger.error(f"Error verifying Twitter session: {str(e)}")
            return False
    
    def scrape_user_profile(self, account: ActorAccount, target_username: str, **kwargs) -> Dict[str, Any]:
        self.logger.warning("Twitter profile scraping not yet implemented")
        return {'error': 'Twitter profile scraping not yet implemented', 'success': False}
    
    def scrape_user_content(self, account: ActorAccount, target_username: str, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape content from a specific Twitter user using enhanced scraper.

        Args:
            account: Authenticated ActorAccount instance
            target_username: Twitter username to scrape
            limit: Maximum number of tweets to return
            **kwargs: Additional scraping parameters

        Returns:
            List of normalized tweet data
        """
        try:
            self.logger.info(f"Starting Twitter user content scraping for @{target_username}")

            # Get account credentials
            username = account.platform_username
            password = account.get_decrypted_password()
            email = account.email

            if not password:
                raise Exception("Account password not available for scraping")

            # Use enhanced scraper with authentication
            from ..scrapers.twitter_scraper import TwitterScraper
            scraper = TwitterScraper(
                email=email,
                username=username,
                password=password,
                max_retries=3,
                delay_between_scrolls=3,
                rate_limit_delay=2
            )

            # Search for user's tweets using from: operator
            user_results = scraper.search_tweets(
                query=f"from:{target_username}",
                count=limit
            )

            # Close scraper
            scraper.close()

            if not user_results.get('success', False):
                error_msg = user_results.get('error', 'Unknown scraping error')
                self.logger.error(f"User scraping failed: {error_msg}")
                raise Exception(f"User scraping failed: {error_msg}")

            tweets = user_results.get('tweets', [])
            self.logger.info(f"Successfully scraped {len(tweets)} tweets from @{target_username}")

            # Normalize results
            normalized_results = []
            for tweet in tweets:
                normalized_result = self.normalize_data(tweet, 'tweet')
                normalized_results.append(normalized_result)

            return normalized_results

        except Exception as e:
            self.logger.error(f"Twitter user content scraping failed: {str(e)}")
            raise e
    
    def search_content(self, account: ActorAccount, keywords: List[str], limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for Twitter content by keywords using enhanced scraper.

        Args:
            account: Authenticated ActorAccount instance
            keywords: List of search keywords
            limit: Maximum number of tweets to return
            **kwargs: Additional search parameters

        Returns:
            List of normalized tweet data
        """
        try:
            self.logger.info(f"Starting Twitter content search for keywords: {keywords}")

            # Get account credentials
            username = account.platform_username
            password = account.get_decrypted_password()
            email = account.email

            if not password:
                raise Exception("Account password not available for scraping")

            # Convert keywords list to search query
            search_query = ' '.join(keywords) if isinstance(keywords, list) else str(keywords)

            self.logger.info(f"Starting enhanced content search for keywords: {search_query}")

            # Get additional parameters from kwargs
            start_date = kwargs.get('start_date')
            end_date = kwargs.get('end_date')
            target_username = kwargs.get('username')
            hashtag = kwargs.get('hashtag')

            # Use enhanced scraper with authentication
            from ..scrapers.twitter_scraper import TwitterScraper
            scraper = TwitterScraper(
                email=email,
                username=username,
                password=password,
                max_retries=3,
                delay_between_scrolls=3,
                rate_limit_delay=2
            )

            # Search with enhanced parameters
            search_results = scraper.search_tweets(
                query=search_query,
                count=limit,
                start_date=start_date,
                end_date=end_date,
                username=target_username,
                hashtag=hashtag
            )

            # Close scraper
            scraper.close()

            # Check if scraping was successful
            if not search_results.get('success', False):
                error_msg = search_results.get('error', 'Unknown scraping error')
                self.logger.error(f"Enhanced scraping failed: {error_msg}")
                raise Exception(f"Enhanced scraping failed: {error_msg}")

            # Get tweets from results
            tweets = search_results.get('tweets', [])
            self.logger.info(f"Successfully scraped {len(tweets)} tweets using enhanced scraper")

            # Normalize results
            normalized_results = []
            for tweet in tweets:
                normalized_result = self.normalize_data(tweet, 'tweet')
                normalized_results.append(normalized_result)

            self.logger.info(f"Returning {len(normalized_results)} normalized results")
            return normalized_results

        except Exception as e:
            self.logger.error(f"Twitter content search failed: {str(e)}")
            raise e
    
    def scrape_my_content(self, account: ActorAccount, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape the authenticated user's own content using enhanced scraper.
        """
        try:
            self.logger.info(f"Starting Twitter my content scraping for @{account.platform_username}")

            # Get account credentials
            username = account.platform_username
            password = account.get_decrypted_password()
            email = account.email

            if not password:
                raise Exception("Account password not available for scraping")

            # Use enhanced scraper with authentication
            from ..scrapers.twitter_scraper import TwitterScraper
            scraper = TwitterScraper(
                email=email,
                username=username,
                password=password,
                max_retries=3,
                delay_between_scrolls=3,
                rate_limit_delay=2
            )

            # Search for own tweets using from: operator
            user_results = scraper.search_tweets(
                query=f"from:{username}",
                count=limit
            )

            # Close scraper
            scraper.close()

            if not user_results.get('success', False):
                error_msg = user_results.get('error', 'Unknown scraping error')
                self.logger.error(f"My content scraping failed: {error_msg}")
                raise Exception(f"My content scraping failed: {error_msg}")

            tweets = user_results.get('tweets', [])
            self.logger.info(f"Successfully scraped {len(tweets)} of my tweets")

            # Normalize results
            normalized_results = []
            for tweet in tweets:
                normalized_result = self.normalize_data(tweet, 'tweet')
                normalized_results.append(normalized_result)

            return normalized_results

        except Exception as e:
            self.logger.error(f"Twitter my content scraping failed: {str(e)}")
            raise e

    def scrape_feed(self, account: ActorAccount, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape the user's Twitter feed/timeline using enhanced scraper.
        """
        try:
            self.logger.info(f"Starting Twitter feed scraping for @{account.platform_username}")

            # Get account credentials
            username = account.platform_username
            password = account.get_decrypted_password()
            email = account.email

            if not password:
                raise Exception("Account password not available for scraping")

            # Use enhanced scraper with authentication
            from ..scrapers.twitter_scraper import TwitterScraper
            scraper = TwitterScraper(
                email=email,
                username=username,
                password=password,
                max_retries=3,
                delay_between_scrolls=3,
                rate_limit_delay=2
            )

            # For feed, we'll search for general trending content from Indonesia
            feed_query = kwargs.get('feed_query', 'trending news Indonesia')
            feed_results = scraper.search_tweets(feed_query, limit)

            # Close scraper
            scraper.close()

            if not feed_results.get('success', False):
                error_msg = feed_results.get('error', 'Unknown scraping error')
                self.logger.error(f"Feed scraping failed: {error_msg}")
                raise Exception(f"Feed scraping failed: {error_msg}")

            tweets = feed_results.get('tweets', [])
            self.logger.info(f"Successfully scraped {len(tweets)} feed tweets")

            # Normalize results
            normalized_results = []
            for tweet in tweets:
                normalized_result = self.normalize_data(tweet, 'tweet')
                normalized_results.append(normalized_result)

            return normalized_results

        except Exception as e:
            self.logger.error(f"Twitter feed scraping failed: {str(e)}")
            raise e

    def normalize_data(self, raw_data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        Normalize Twitter-specific data to common format, supporting both API and enhanced scraper formats.

        Args:
            raw_data: Raw Twitter data
            data_type: Type of data (tweet, user, etc.)

        Returns:
            Normalized data dictionary
        """
        # Debug logging
        self.logger.debug(f"Normalizing data: type={type(raw_data)}, data_type={data_type}")

        if not isinstance(raw_data, dict):
            self.logger.error(f"Expected dict but got {type(raw_data)}: {raw_data}")
            return {}

        # Start with base normalization
        normalized = {
            'platform': 'twitter',
            'data_type': data_type,
            'raw_data': raw_data,
            'normalized_at': datetime.now().isoformat()
        }

        # Twitter-specific normalization
        if data_type == 'tweet':
            # Handle enhanced scraper format (from our enhanced TwitterScraper)
            if 'author_username' in raw_data:
                # Enhanced scraper format
                normalized.update({
                    'id': raw_data.get('id', ''),
                    'author': raw_data.get('author_username', ''),
                    'author_name': raw_data.get('author_name', ''),
                    'title': raw_data.get('text', ''),
                    'content': raw_data.get('text', ''),
                    'url': raw_data.get('url', ''),
                    'created_at': raw_data.get('date', ''),
                    'metrics': {
                        'likes': raw_data.get('likes', 0),
                        'retweets': raw_data.get('retweets', 0),
                        'replies': raw_data.get('replies', 0),
                        'quotes': 0  # Not available in enhanced scraper
                    },
                    'hashtags': [],  # Could be extracted from text if needed
                    'mentions': [],  # Could be extracted from text if needed
                    'media': {
                        'has_media': raw_data.get('has_media', False),
                        'media_count': raw_data.get('media_count', 0)
                    },
                    'verified': raw_data.get('author_verified', False),
                    'language': 'en'  # Default, could be detected
                })
            else:
                # Traditional API format
                normalized.update({
                    'id': raw_data.get('id_str') or str(raw_data.get('id', '')),
                    'author': raw_data.get('user', {}).get('screen_name') or raw_data.get('author'),
                    'author_name': raw_data.get('user', {}).get('name') or raw_data.get('author_name'),
                    'title': raw_data.get('full_text') or raw_data.get('text') or raw_data.get('content'),
                    'content': raw_data.get('full_text') or raw_data.get('text') or raw_data.get('content'),
                    'url': f"https://twitter.com/{raw_data.get('user', {}).get('screen_name', 'unknown')}/status/{raw_data.get('id_str', raw_data.get('id', 'unknown'))}",
                    'created_at': raw_data.get('created_at'),
                    'metrics': {
                        'likes': raw_data.get('favorite_count', 0),
                        'retweets': raw_data.get('retweet_count', 0),
                        'replies': raw_data.get('reply_count', 0),
                        'quotes': raw_data.get('quote_count', 0)
                    },
                    'hashtags': [tag.get('text', '') for tag in raw_data.get('entities', {}).get('hashtags', [])],
                    'mentions': [mention.get('screen_name', '') for mention in raw_data.get('entities', {}).get('user_mentions', [])],
                    'media': raw_data.get('entities', {}).get('media', []),
                    'verified': raw_data.get('user', {}).get('verified', False),
                    'language': raw_data.get('lang', 'en')
                })

        # Preserve enhanced scraper flags
        if raw_data.get('source') == 'enhanced_selenium_scraper':
            normalized['source'] = 'enhanced_selenium_scraper'
            normalized['enhanced_scraper'] = True

        return normalized

    def get_platform_specific_id(self, data: Dict[str, Any]) -> str:
        """
        Extract platform-specific content ID from Twitter data.

        Args:
            data: Normalized data dictionary

        Returns:
            Twitter-specific content ID
        """
        return data.get('id') or data.get('id_str') or data.get('tweet_id')

    def _scrape_real_content(self, search_query: str, limit: int, task_parameters: Dict = None) -> Dict[str, Any]:
        """
        Scrape real Twitter content using the real scraper with regional filtering.
        """
        try:
            from ..scrapers.twitter_scraper import TwitterScraper

            # Extract regional settings from task parameters
            region = 'global'
            if task_parameters:
                region = task_parameters.get('region', 'global')

            self.logger.info(f"Using real Twitter scraper for query: {search_query} (Region: {region})")

            scraper = TwitterScraper()
            result = scraper.search_tweets(search_query, limit, region=region)
            scraper.close()

            if result.get('success'):
                # Convert to expected format
                return {
                    'success': True,
                    'tweets': result.get('tweets', []),
                    'total_found': len(result.get('tweets', [])),
                    'query': search_query,
                    'region': region,
                    'timestamp': datetime.now().isoformat(),
                    'real_scraper': True
                }
            else:
                self.logger.error(f"Real scraper failed: {result.get('error')}")
                raise Exception(f"Real scraper failed: {result.get('error')}")

        except Exception as e:
            self.logger.error(f"Real scraping error: {str(e)}")
            raise e





# Register the Twitter engine
EngineRegistry.register('twitter', TwitterEngine)
