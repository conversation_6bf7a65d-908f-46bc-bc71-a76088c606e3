"""
TikTok Engine for Actor Platform System

This module implements the TikTok-specific engine that handles authentication,
scraping, and data processing for TikTok platform.
"""

from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings

from .base_engine import BaseActorEngine, EngineRegistry
from ..utils.tiktok_auth import TikTokAuthenticator
from ..scrapers.tiktok_scraper import TikTokScraper
from ..models import ActorAccount, TikTokUserAccount

logger = logging.getLogger(__name__)


class TikTokEngine(BaseActorEngine):
    """
    TikTok implementation of the Actor engine.
    
    This engine handles all TikTok-specific operations including authentication,
    content scraping, and data normalization.
    """
    
    def __init__(self, platform: str = 'tiktok'):
        super().__init__(platform)
        self.authenticator = TikTokAuthenticator()
        self.scraper = None
        self._current_account = None  # Track current logged-in account
        self._session_active = False  # Track if session is active

    def __del__(self):
        """Ensure session is closed when engine is destroyed"""
        self.close_session()
    
    def authenticate(self, account: ActorAccount, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Authenticate with TikTok using account credentials.

        Args:
            account: ActorAccount instance with TikTok credentials
            credentials: Additional authentication parameters

        Returns:
            Authentication result dictionary
        """
        try:
            self.logger.info(f"Starting TikTok authentication for @{account.platform_username}")

            # Get credentials
            username = account.platform_username
            password = account.get_decrypted_password()

            if not password:
                return {
                    'success': False,
                    'error': 'No password available for TikTok authentication'
                }

            # Real TikTok authentication using TikTokAuthenticator
            self.logger.info("Starting real TikTok authentication")

            # Use real TikTok authenticator - NO MOCK DATA
            auth_result = self.authenticator.login(username, password)

            if not auth_result.get('success'):
                return {
                    'success': False,
                    'error': f'TikTok authentication failed: {auth_result.get("error", "Unknown error")}'
                }

            # Save real session data from authenticator
            session_data = auth_result.get('session_data', {})
            if session_data:
                account.encrypt_session_data(session_data)
                account.last_login = timezone.now()
                account.session_expires_at = timezone.now() + timedelta(hours=24)
                account.save()

            return {
                'success': True,
                'message': f'TikTok authentication successful for @{username}',
                'session_data': session_data
            }

        except Exception as e:
            self.logger.error(f"TikTok authentication failed: {str(e)}")
            return {
                'success': False,
                'error': f'Authentication failed: {str(e)}'
            }
    
    def verify_session(self, account: ActorAccount) -> bool:
        """
        Verify if the TikTok session is still valid.

        Args:
            account: ActorAccount instance to verify

        Returns:
            True if session is valid, False otherwise
        """
        try:
            session_data = account.decrypt_session_data()

            if not session_data:
                return False

            # Check if session has expired
            expires_at = session_data.get('expires_at')
            if expires_at:
                expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00').replace('+00:00', ''))
                if datetime.now() > expiry_time:
                    self.logger.info(f"TikTok session expired for @{account.platform_username}")
                    return False

            # Check if required session data exists
            required_fields = ['authenticated', 'username']
            for field in required_fields:
                if not session_data.get(field):
                    self.logger.warning(f"Missing required session field: {field}")
                    return False

            self.logger.info(f"TikTok session valid for @{account.platform_username}")
            return True

        except Exception as e:
            self.logger.error(f"Error verifying TikTok session: {str(e)}")
            return False

    def _get_authenticated_scraper(self, account: ActorAccount) -> 'TikTokPersistentSelenium':
        """
        Get an authenticated TikTok scraper with persistent session (non-incognito mode).
        This method ensures we only login once and reuse the browser session.

        Args:
            account: ActorAccount instance

        Returns:
            Authenticated TikTokPersistentSelenium instance
        """
        # Check if we already have an active scraper for this account
        if (self.scraper and
            self._session_active and
            self._current_account and
            self._current_account.id == account.id):

            self.logger.info(f"♻️ Reusing existing TikTok persistent session for @{account.platform_username}")
            return self.scraper

        # Close existing scraper if it's for a different account
        if self.scraper:
            self.logger.info("🔄 Closing previous TikTok persistent session")
            self.scraper.close()
            self.scraper = None
            self._session_active = False
            self._current_account = None

        # Create new persistent scraper
        self.logger.info(f"🆕 Creating new TikTok persistent session for @{account.platform_username}")

        from ..scrapers.tiktok_persistent_selenium import TikTokPersistentSelenium

        # Use account-specific profile directory
        profile_dir = f"tiktok_profile_{account.platform_username}_{account.id}"
        self.scraper = TikTokPersistentSelenium(user_data_dir=profile_dir)

        # Get credentials
        username = account.platform_username
        password = account.get_decrypted_password()

        if not password:
            raise Exception("Account password not available for TikTok scraping")

        # Login with persistent session (checks existing session first)
        self.logger.info(f"🔐 Authenticating TikTok as @{username} (persistent session)")
        login_result = self.scraper.login(username, password)

        if not login_result.get('success'):
            self.scraper.close()
            self.scraper = None
            raise Exception(f"TikTok login failed: {login_result.get('error', 'Unknown error')}")

        # Mark session as active
        self._session_active = True
        self._current_account = account

        if login_result.get('session_reused'):
            self.logger.info(f"✅ TikTok persistent session reused for @{username} - no login required")
        else:
            self.logger.info(f"✅ TikTok persistent session established for @{username} - saved for future use")

        return self.scraper

    def close_session(self):
        """Close the current TikTok session"""
        if self.scraper:
            self.logger.info("🔒 Closing TikTok session")
            self.scraper.close()
            self.scraper = None
            self._session_active = False
            self._current_account = None
    
    def scrape_user_profile(self, account: ActorAccount, target_username: str, **kwargs) -> Dict[str, Any]:
        """
        Scrape TikTok user profile data.
        
        Args:
            account: ActorAccount instance
            target_username: TikTok username to scrape
            **kwargs: Additional parameters
            
        Returns:
            Dict containing scraped profile data
        """
        try:
            if not self.verify_session(account):
                raise Exception("Invalid session, please re-authenticate")
            
            # Initialize scraper if not already done
            if not self.scraper:
                self.scraper = TikTokScraper()
            
            # Get session data
            session_data = account.decrypt_session_data()
            
            # Scrape profile data
            profile_data = self.scraper.scrape_user_profile(
                target_username, 
                session_data=session_data,
                **kwargs
            )
            
            return self.normalize_data(profile_data, 'user')
            
        except Exception as e:
            self.logger.error(f"Profile scraping error: {str(e)}")
            return {'error': str(e)}
    
    def scrape_user_content(self, account: ActorAccount, target_username: str, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape content from a specific TikTok user using enhanced scraper.

        Args:
            account: Authenticated ActorAccount instance
            target_username: TikTok username to scrape
            limit: Maximum number of videos to return
            **kwargs: Additional scraping parameters

        Returns:
            List of normalized video data
        """
        try:
            self.logger.info(f"Starting TikTok user content scraping for @{target_username}")

            # Get authenticated scraper (reuses session if available)
            scraper = self._get_authenticated_scraper(account)

            # Get user videos using existing session
            user_results = scraper.get_user_videos(target_username, limit)

            if not user_results.get('success', False):
                error_msg = user_results.get('error', 'Unknown scraping error')
                self.logger.error(f"User scraping failed: {error_msg}")
                raise Exception(f"User scraping failed: {error_msg}")

            videos = user_results.get('videos', [])
            self.logger.info(f"Successfully scraped {len(videos)} videos from @{target_username}")

            # Normalize results
            normalized_results = []
            for video in videos:
                normalized_result = self.normalize_data(video, 'video')
                normalized_results.append(normalized_result)

            return normalized_results

        except Exception as e:
            self.logger.error(f"TikTok user content scraping failed: {str(e)}")
            raise e
    
    def search_content(self, account: ActorAccount, keywords: List[str], limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for TikTok content by keywords using enhanced scraper.

        Args:
            account: Authenticated ActorAccount instance
            keywords: List of search keywords
            limit: Maximum number of videos to return
            **kwargs: Additional search parameters

        Returns:
            List of normalized video data
        """
        try:
            self.logger.info(f"Starting TikTok content search for keywords: {keywords}")

            # Convert keywords list to search query
            search_query = ' '.join(keywords) if isinstance(keywords, list) else str(keywords)

            self.logger.info(f"Starting content search for keywords: {search_query}")

            # Get additional parameters from kwargs
            hashtag = kwargs.get('hashtag')

            # Get authenticated scraper (reuses session if available)
            scraper = self._get_authenticated_scraper(account)

            # Search with enhanced parameters using persistent session
            if hashtag:
                # Search by hashtag
                search_results = scraper.search_videos_by_hashtag(hashtag, limit)
            else:
                # General search
                search_results = scraper.search_videos(search_query, limit)

            # Don't close scraper - keep session active for reuse

            # Check if scraping was successful
            if not search_results.get('success', False):
                error_msg = search_results.get('error', 'Unknown scraping error')
                self.logger.error(f"Enhanced scraping failed: {error_msg}")
                raise Exception(f"Enhanced scraping failed: {error_msg}")

            # Get videos from results
            videos = search_results.get('videos', [])
            self.logger.info(f"Successfully scraped {len(videos)} videos using enhanced scraper")

            # Normalize results
            normalized_results = []
            for video in videos:
                normalized_result = self.normalize_data(video, 'video')
                normalized_results.append(normalized_result)

            self.logger.info(f"Returning {len(normalized_results)} normalized results")
            return normalized_results

        except Exception as e:
            self.logger.error(f"TikTok content search failed: {str(e)}")
            raise e
    
    def scrape_my_content(self, account: ActorAccount, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape the authenticated user's own content using enhanced scraper.
        """
        try:
            self.logger.info(f"Starting TikTok my content scraping for @{account.platform_username}")

            # Get authenticated scraper (reuses session if available)
            scraper = self._get_authenticated_scraper(account)

            # Get own videos using persistent session
            username = account.platform_username
            user_results = scraper.get_user_videos(username, limit)

            # Don't close scraper - keep session active for reuse

            if not user_results.get('success', False):
                error_msg = user_results.get('error', 'Unknown scraping error')
                self.logger.error(f"My content scraping failed: {error_msg}")
                raise Exception(f"My content scraping failed: {error_msg}")

            videos = user_results.get('videos', [])
            self.logger.info(f"Successfully scraped {len(videos)} of my videos")

            # Normalize results
            normalized_results = []
            for video in videos:
                normalized_result = self.normalize_data(video, 'video')
                normalized_results.append(normalized_result)

            return normalized_results

        except Exception as e:
            self.logger.error(f"TikTok my content scraping failed: {str(e)}")
            raise e
    
    def scrape_feed(self, account: ActorAccount, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape the user's TikTok feed/timeline using enhanced scraper.
        """
        try:
            self.logger.info(f"Starting TikTok feed scraping for @{account.platform_username}")

            # Get authenticated scraper (reuses session if available)
            scraper = self._get_authenticated_scraper(account)

            # For feed, we'll scrape trending content using persistent session
            feed_query = kwargs.get('feed_query', 'trending')
            feed_results = scraper.search_videos(feed_query, limit)

            # Don't close scraper - keep session active for reuse

            if not feed_results.get('success', False):
                error_msg = feed_results.get('error', 'Unknown scraping error')
                self.logger.error(f"Feed scraping failed: {error_msg}")
                raise Exception(f"Feed scraping failed: {error_msg}")

            videos = feed_results.get('videos', [])
            self.logger.info(f"Successfully scraped {len(videos)} feed videos")

            # Normalize results
            normalized_results = []
            for video in videos:
                normalized_result = self.normalize_data(video, 'video')
                normalized_results.append(normalized_result)

            return normalized_results

        except Exception as e:
            self.logger.error(f"TikTok feed scraping failed: {str(e)}")
            raise e
    
    def normalize_data(self, raw_data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        Normalize TikTok-specific data to common format, supporting both API and enhanced scraper formats.

        Args:
            raw_data: Raw TikTok data
            data_type: Type of data (video, user, etc.)

        Returns:
            Normalized data dictionary
        """
        # Debug logging
        self.logger.debug(f"Normalizing data: type={type(raw_data)}, data_type={data_type}")

        if not isinstance(raw_data, dict):
            self.logger.error(f"Expected dict but got {type(raw_data)}: {raw_data}")
            return {}

        # Start with base normalization
        normalized = {
            'platform': 'tiktok',
            'data_type': data_type,
            'raw_data': raw_data,
            'normalized_at': datetime.now().isoformat()
        }

        # TikTok-specific normalization
        if data_type == 'video':
            # Handle enhanced scraper format (from our enhanced TikTokScraper)
            if 'author_username' in raw_data:
                # Enhanced scraper format
                normalized.update({
                    'id': raw_data.get('id', ''),
                    'author': raw_data.get('author_username', ''),
                    'author_name': raw_data.get('author_name', ''),
                    'title': raw_data.get('description', ''),
                    'content': raw_data.get('description', ''),
                    'url': raw_data.get('url', ''),
                    'created_at': raw_data.get('date', ''),
                    'metrics': {
                        'likes': raw_data.get('likes', 0),
                        'comments': raw_data.get('comments', 0),
                        'shares': raw_data.get('shares', 0),
                        'views': raw_data.get('views', 0)
                    },
                    'hashtags': raw_data.get('hashtags', []),
                    'music': raw_data.get('music', ''),
                    'duration': raw_data.get('duration', 0),
                    'video_url': raw_data.get('video_url', ''),
                    'thumbnail_url': raw_data.get('thumbnail_url', ''),
                    'verified': raw_data.get('author_verified', False)
                })
            else:
                # Traditional API format
                normalized.update({
                    'id': raw_data.get('id') or raw_data.get('aweme_id'),
                    'author': raw_data.get('author', {}).get('uniqueId', ''),
                    'author_name': raw_data.get('author', {}).get('nickname', ''),
                    'title': raw_data.get('desc', ''),
                    'content': raw_data.get('desc', ''),
                    'url': f"https://www.tiktok.com/@{raw_data.get('author', {}).get('uniqueId', 'unknown')}/video/{raw_data.get('id', 'unknown')}",
                    'created_at': raw_data.get('createTime', ''),
                    'metrics': {
                        'likes': raw_data.get('stats', {}).get('diggCount', 0),
                        'comments': raw_data.get('stats', {}).get('commentCount', 0),
                        'shares': raw_data.get('stats', {}).get('shareCount', 0),
                        'views': raw_data.get('stats', {}).get('playCount', 0)
                    },
                    'hashtags': [tag.get('title', '') for tag in raw_data.get('challenges', [])],
                    'music': raw_data.get('music', {}).get('title', ''),
                    'duration': raw_data.get('video', {}).get('duration', 0),
                    'video_url': raw_data.get('video', {}).get('playAddr', ''),
                    'thumbnail_url': raw_data.get('video', {}).get('cover', ''),
                    'verified': raw_data.get('author', {}).get('verified', False)
                })

        elif data_type == 'user':
            normalized.update({
                'username': raw_data.get('uniqueId', ''),
                'display_name': raw_data.get('nickname', ''),
                'bio': raw_data.get('signature', ''),
                'followers': raw_data.get('stats', {}).get('followerCount', 0),
                'following': raw_data.get('stats', {}).get('followingCount', 0),
                'likes': raw_data.get('stats', {}).get('heartCount', 0),
                'videos': raw_data.get('stats', {}).get('videoCount', 0),
                'avatar_url': raw_data.get('avatarMedium', ''),
                'verified': raw_data.get('verified', False),
            })

        # Preserve enhanced scraper flags
        if raw_data.get('source') == 'real_tiktok_scraper':
            normalized['source'] = 'real_tiktok_scraper'
            normalized['enhanced_scraper'] = True

        return normalized
    
    def get_platform_specific_id(self, data: Dict[str, Any]) -> str:
        """
        Extract platform-specific content ID from TikTok data.

        Args:
            data: Normalized data dictionary

        Returns:
            TikTok-specific content ID
        """
        return data.get('id') or data.get('aweme_id') or data.get('video_id')

    def _scrape_real_content(self, search_query: str, limit: int, task_parameters: Dict = None) -> Dict[str, Any]:
        """
        Scrape real TikTok content using the real scraper with enhanced features.
        """
        try:
            from ..scrapers.tiktok_scraper import TikTokScraper

            self.logger.info(f"Using real TikTok scraper for query: {search_query}")

            scraper = TikTokScraper()
            result = scraper.search_videos(search_query, limit)
            scraper.close()

            if result.get('success'):
                # Convert to expected format
                return {
                    'success': True,
                    'videos': result.get('videos', []),
                    'total_found': len(result.get('videos', [])),
                    'query': search_query,
                    'timestamp': datetime.now().isoformat(),
                    'real_scraper': True
                }
            else:
                self.logger.error(f"Real scraper failed: {result.get('error')}")
                raise Exception(f"Real scraper failed: {result.get('error')}")

        except Exception as e:
            self.logger.error(f"Real scraping error: {str(e)}")
            raise e




# Register the TikTok engine
EngineRegistry.register('tiktok', TikTokEngine)
