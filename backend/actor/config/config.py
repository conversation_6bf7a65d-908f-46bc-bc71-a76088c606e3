"""
Actor Platform Configuration

Configuration settings for the Actor platform system including
anti-detection, scraping, and platform-specific settings.
"""

import random
from typing import Dict, List, Any

# Anti-Detection Configuration
ANTI_DETECTION_CONFIG = {
    'user_agents': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
    ],
    'viewport_sizes': [
        (1920, 1080),
        (1366, 768),
        (1440, 900),
        (1536, 864),
        (1280, 720)
    ],
    'request_delays': {
        'min_delay': 2.0,
        'max_delay': 5.0,
        'human_delay_min': 0.5,
        'human_delay_max': 2.0
    },
    'browser_options': {
        'disable_images': False,
        'disable_javascript': False,
        'disable_plugins': True,
        'disable_extensions': True,
        'incognito_mode': True
    },
    'fingerprint_randomization': {
        'randomize_timezone': True,
        'randomize_language': True,
        'randomize_platform': True,
        'randomize_screen_resolution': True
    }
}

# Rate Limiting Configuration
RATE_LIMITING_CONFIG = {
    'requests_per_minute': 30,
    'requests_per_hour': 1000,
    'concurrent_sessions': 3,
    'cooldown_period': 300,  # 5 minutes
    'backoff_multiplier': 2.0,
    'max_retries': 3
}

# Session Management Configuration
SESSION_CONFIG = {
    'session_timeout': 3600,  # 1 hour
    'max_session_age': 86400,  # 24 hours
    'session_refresh_interval': 1800,  # 30 minutes
    'max_concurrent_sessions': 5,
    'session_cleanup_interval': 300,  # 5 minutes
    'persistent_sessions': True
}

# Proxy Configuration
PROXY_CONFIG = {
    'enabled': False,
    'rotation_enabled': False,
    'proxy_list': [],
    'proxy_timeout': 10,
    'max_proxy_failures': 3,
    'proxy_health_check_interval': 300
}

# Platform-Specific Configuration
PLATFORM_CONFIG = {
    'tiktok': {
        'base_url': 'https://www.tiktok.com',
        'login_url': 'https://www.tiktok.com/login/phone-or-email/email',
        'search_url': 'https://www.tiktok.com/search',
        'max_videos_per_request': 50,
        'max_scroll_attempts': 10,
        'scroll_delay': 2.0,
        'selectors': {
            'video_container': '[data-e2e="user-post-item"]',
            'video_link': 'a[href*="/video/"]',
            'video_description': '[data-e2e="browse-video-desc"]',
            'like_count': '[data-e2e="like-count"]',
            'comment_count': '[data-e2e="comment-count"]',
            'share_count': '[data-e2e="share-count"]',
            'user_name': '[data-e2e="user-title"]',
            'user_avatar': '[data-e2e="avatar"]'
        }
    },
    'twitter': {
        'base_url': 'https://twitter.com',
        'login_url': 'https://twitter.com/i/flow/login',
        'search_url': 'https://twitter.com/search',
        'max_tweets_per_request': 50,
        'max_scroll_attempts': 10,
        'scroll_delay': 2.0,
        'selectors': {
            'tweet_container': '[data-testid="tweet"]',
            'tweet_text': '[data-testid="tweetText"]',
            'like_count': '[data-testid="like"]',
            'retweet_count': '[data-testid="retweet"]',
            'reply_count': '[data-testid="reply"]',
            'user_name': '[data-testid="User-Name"]',
            'user_avatar': '[data-testid="UserAvatar-Container-unknown"]'
        }
    }
}

# Scraping Configuration
SCRAPING_CONFIG = {
    'max_videos_per_profile': 100,
    'max_comments_per_video': 200,
    'max_search_results': 500,
    'max_trending_videos': 200,
    'content_filters': {
        'min_video_duration': 5,  # seconds
        'max_video_duration': 600,  # 10 minutes
        'exclude_private_accounts': True,
        'exclude_deleted_content': True
    },
    'data_extraction': {
        'extract_video_metadata': True,
        'extract_user_metadata': True,
        'extract_engagement_metrics': True,
        'extract_hashtags': True,
        'extract_mentions': True,
        'extract_music_info': True,
        'extract_effects_info': True
    },
    'quality_checks': {
        'validate_data_completeness': True,
        'check_duplicate_content': True,
        'verify_data_integrity': True
    }
}

# Error Handling Configuration
ERROR_HANDLING_CONFIG = {
    'max_retries': 3,
    'retry_delay': 5.0,
    'exponential_backoff': True,
    'capture_screenshots_on_error': True,
    'log_detailed_errors': True,
    'continue_on_error': True
}

# Monitoring Configuration
MONITORING_CONFIG = {
    'enable_performance_monitoring': True,
    'enable_error_tracking': True,
    'enable_success_rate_tracking': True,
    'metrics_collection_interval': 60,  # seconds
    'alert_thresholds': {
        'error_rate': 0.1,  # 10%
        'response_time': 30.0,  # seconds
        'success_rate': 0.8  # 80%
    }
}

# Regional Configuration for Twitter (Indonesia focus)
REGIONAL_CONFIG = {
    'twitter': {
        'default_region': 'indonesia',
        'language_codes': ['id', 'en'],
        'timezone': 'Asia/Jakarta',
        'trending_locations': [
            {'woeid': 1047378, 'name': 'Indonesia'},
            {'woeid': 1047180, 'name': 'Jakarta'},
            {'woeid': 1044316, 'name': 'Surabaya'},
            {'woeid': 1044316, 'name': 'Bandung'}
        ]
    }
}

def get_random_user_agent() -> str:
    """Get a random user agent from the configured list"""
    return random.choice(ANTI_DETECTION_CONFIG['user_agents'])

def get_random_viewport() -> tuple:
    """Get a random viewport size from the configured list"""
    return random.choice(ANTI_DETECTION_CONFIG['viewport_sizes'])

def get_platform_config(platform: str) -> Dict[str, Any]:
    """Get configuration for a specific platform"""
    return PLATFORM_CONFIG.get(platform, {})

def get_request_delay() -> float:
    """Get a random request delay within configured bounds"""
    delays = ANTI_DETECTION_CONFIG['request_delays']
    return random.uniform(delays['min_delay'], delays['max_delay'])

def get_human_delay() -> float:
    """Get a random human-like delay"""
    delays = ANTI_DETECTION_CONFIG['request_delays']
    return random.uniform(delays['human_delay_min'], delays['human_delay_max'])
