import time
import random
import logging
import json
import re
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from .anti_detection import AntiDetectionManager
from .tiktok_auth import TikTokAuthenticator

logger = logging.getLogger(__name__)

class ActorTikTokScraper:
    """
    TikTok scraper for authenticated users with anti-detection measures
    """
    
    def __init__(self, session_data=None, headless=True, proxy=None):
        self.session_data = session_data
        self.headless = headless
        self.proxy = proxy
        self.driver = None
        self.anti_detection = AntiDetectionManager()
        self.authenticator = SimpleTikTokAuthenticator()
        
        # TikTok URLs
        self.base_url = "https://www.tiktok.com"
        self.profile_url = "https://www.tiktok.com/@{username}"
        self.following_url = "https://www.tiktok.com/@{username}/following"
        self.followers_url = "https://www.tiktok.com/@{username}/followers"
        
        # XPath Selectors
        self.selectors = {
            'video_container': '//*[@data-e2e="user-post-item"]',
            'video_link': '//a[contains(@href, "/video/")]',
            'video_description': '//*[@data-e2e="browse-video-desc"]',
            'video_stats': '//*[@data-e2e="video-views"]',
            'like_count': '//*[@data-e2e="like-count"]',
            'comment_count': '//*[@data-e2e="comment-count"]',
            'share_count': '//*[@data-e2e="share-count"]',
            'user_avatar': '//*[@data-e2e="avatar"]',
            'user_name': '//*[@data-e2e="user-title"]',
            'user_bio': '//*[@data-e2e="user-bio"]',
            'follower_count': '//*[@data-e2e="followers-count"]',
            'following_count': '//*[@data-e2e="following-count"]',
            'likes_count': '//*[@data-e2e="likes-count"]',
            'feed_video': '//*[@data-e2e="recommend-list-item"]',
            'hashtag_challenge': '//*[@data-e2e="challenge-item"]',
            'load_more': '//*[@data-e2e="load-more"]'
        }
    
    def __enter__(self):
        self.setup_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()
    
    def setup_driver(self):
        """
        Setup the WebDriver with anti-detection measures
        """
        try:
            self.driver = self.anti_detection.setup_driver(
                headless=self.headless,
                proxy=self.proxy
            )
            
            # Load session if available
            if self.session_data:
                self._load_session()
            
            logger.info("Actor scraper driver setup complete")
        
        except Exception as e:
            logger.error(f"Error setting up driver: {str(e)}")
            raise
    
    def cleanup(self):
        """
        Clean up resources
        """
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
            logger.info("Actor scraper cleanup complete")
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
    
    def _load_session(self):
        """
        Load session data into the browser
        """
        try:
            if not self.session_data:
                return False
            
            # Navigate to TikTok first
            self.driver.get(self.base_url)
            self.anti_detection.random_delay(2, 4)
            
            # Load cookies
            if 'cookies' in self.session_data:
                for cookie in self.session_data['cookies']:
                    try:
                        self.driver.add_cookie(cookie)
                    except Exception as e:
                        logger.warning(f"Failed to add cookie: {str(e)}")
            
            # Load local storage
            if 'local_storage' in self.session_data:
                for key, value in self.session_data['local_storage'].items():
                    try:
                        self.driver.execute_script(
                            f"localStorage.setItem('{key}', '{value}');"
                        )
                    except Exception as e:
                        logger.warning(f"Failed to set localStorage item: {str(e)}")
            
            # Load session storage
            if 'session_storage' in self.session_data:
                for key, value in self.session_data['session_storage'].items():
                    try:
                        self.driver.execute_script(
                            f"sessionStorage.setItem('{key}', '{value}');"
                        )
                    except Exception as e:
                        logger.warning(f"Failed to set sessionStorage item: {str(e)}")
            
            # Refresh to apply session
            self.driver.refresh()
            self.anti_detection.random_delay(3, 5)
            
            logger.info("Session data loaded successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error loading session: {str(e)}")
            return False
    
    def verify_login_status(self):
        """
        Verify if the user is still logged in
        
        Returns:
            bool: True if logged in, False otherwise
        """
        try:
            self.driver.get(self.base_url)
            self.anti_detection.random_delay(2, 4)
            
            # Check for login indicators using XPath
            login_xpath_indicators = [
                '//*[@data-e2e="profile-icon"]',
                '//*[@data-e2e="nav-profile"]',
                '//a[contains(@href, "/@")]'
            ]

            for selector in login_xpath_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        logger.info("User is logged in")
                        return True
                except NoSuchElementException:
                    continue

            # Check for login button (indicates not logged in)
            try:
                login_button = self.driver.find_element(By.XPATH, '//*[@data-e2e="top-login-button"]')
                if login_button.is_displayed():
                    logger.warning("User is not logged in")
                    return False
            except NoSuchElementException:
                pass
            
            logger.warning("Login status unclear")
            return False
        
        except Exception as e:
            logger.error(f"Error verifying login status: {str(e)}")
            return False
    
    def scrape_my_videos(self, username, limit=50):
        """
        Scrape videos from the authenticated user's profile
        
        Args:
            username: TikTok username
            limit: Maximum number of videos to scrape
        
        Returns:
            list: List of video data
        """
        try:
            videos = []
            profile_url = self.profile_url.format(username=username)
            
            self.driver.get(profile_url)
            self.anti_detection.random_delay(3, 5)
            
            # Simulate human behavior
            self.anti_detection.simulate_human_behavior(self.driver)
            
            # Scroll and collect videos
            videos_collected = 0
            scroll_attempts = 0
            max_scroll_attempts = 20
            
            while videos_collected < limit and scroll_attempts < max_scroll_attempts:
                # Find video containers
                video_containers = self.driver.find_elements(
                    By.XPATH, self.selectors['video_container']
                )
                
                for container in video_containers[videos_collected:]:
                    if videos_collected >= limit:
                        break
                    
                    try:
                        video_data = self._extract_video_data(container)
                        if video_data:
                            videos.append(video_data)
                            videos_collected += 1
                    
                    except Exception as e:
                        logger.warning(f"Error extracting video data: {str(e)}")
                        continue
                
                # Scroll for more videos
                if videos_collected < limit:
                    self._scroll_for_more_content()
                    scroll_attempts += 1
                    self.anti_detection.random_delay(2, 4)
            
            logger.info(f"Scraped {len(videos)} videos from profile: {username}")
            return videos
        
        except Exception as e:
            logger.error(f"Error scraping my videos: {str(e)}")
            return []
    
    def scrape_my_followers(self, username, limit=100):
        """
        Scrape followers from the authenticated user's profile
        
        Args:
            username: TikTok username
            limit: Maximum number of followers to scrape
        
        Returns:
            list: List of follower data
        """
        try:
            followers = []
            followers_url = self.followers_url.format(username=username)
            
            self.driver.get(followers_url)
            self.anti_detection.random_delay(3, 5)
            
            # Check if followers list is accessible
            if self._is_private_or_restricted():
                logger.warning("Followers list is private or restricted")
                return []
            
            followers_collected = 0
            scroll_attempts = 0
            max_scroll_attempts = 15
            
            while followers_collected < limit and scroll_attempts < max_scroll_attempts:
                # Find follower elements
                follower_elements = self.driver.find_elements(
                    By.XPATH, '//*[@data-e2e="user-item"]'
                )
                
                for element in follower_elements[followers_collected:]:
                    if followers_collected >= limit:
                        break
                    
                    try:
                        follower_data = self._extract_user_data(element)
                        if follower_data:
                            followers.append(follower_data)
                            followers_collected += 1
                    
                    except Exception as e:
                        logger.warning(f"Error extracting follower data: {str(e)}")
                        continue
                
                # Scroll for more followers
                if followers_collected < limit:
                    self._scroll_for_more_content()
                    scroll_attempts += 1
                    self.anti_detection.random_delay(2, 4)
            
            logger.info(f"Scraped {len(followers)} followers for: {username}")
            return followers
        
        except Exception as e:
            logger.error(f"Error scraping followers: {str(e)}")
            return []
    
    def scrape_my_following(self, username, limit=100):
        """
        Scrape following list from the authenticated user's profile
        
        Args:
            username: TikTok username
            limit: Maximum number of following to scrape
        
        Returns:
            list: List of following data
        """
        try:
            following = []
            following_url = self.following_url.format(username=username)
            
            self.driver.get(following_url)
            self.anti_detection.random_delay(3, 5)
            
            # Check if following list is accessible
            if self._is_private_or_restricted():
                logger.warning("Following list is private or restricted")
                return []
            
            following_collected = 0
            scroll_attempts = 0
            max_scroll_attempts = 15
            
            while following_collected < limit and scroll_attempts < max_scroll_attempts:
                # Find following elements
                following_elements = self.driver.find_elements(
                    By.XPATH, '//*[@data-e2e="user-item"]'
                )
                
                for element in following_elements[following_collected:]:
                    if following_collected >= limit:
                        break
                    
                    try:
                        following_data = self._extract_user_data(element)
                        if following_data:
                            following.append(following_data)
                            following_collected += 1
                    
                    except Exception as e:
                        logger.warning(f"Error extracting following data: {str(e)}")
                        continue
                
                # Scroll for more following
                if following_collected < limit:
                    self._scroll_for_more_content()
                    scroll_attempts += 1
                    self.anti_detection.random_delay(2, 4)
            
            logger.info(f"Scraped {len(following)} following for: {username}")
            return following
        
        except Exception as e:
            logger.error(f"Error scraping following: {str(e)}")
            return []
    
    def scrape_my_likes(self, username, limit=50):
        """
        Scrape liked videos from the authenticated user's profile
        
        Args:
            username: TikTok username
            limit: Maximum number of liked videos to scrape
        
        Returns:
            list: List of liked video data
        """
        try:
            likes = []
            likes_url = f"{self.base_url}/@{username}/likes"
            
            self.driver.get(likes_url)
            self.anti_detection.random_delay(3, 5)
            
            # Check if likes are private
            if self._is_private_or_restricted():
                logger.warning("Likes are private or restricted")
                return []
            
            likes_collected = 0
            scroll_attempts = 0
            max_scroll_attempts = 15
            
            while likes_collected < limit and scroll_attempts < max_scroll_attempts:
                # Find liked video containers
                video_containers = self.driver.find_elements(
                    By.XPATH, self.selectors['video_container']
                )
                
                for container in video_containers[likes_collected:]:
                    if likes_collected >= limit:
                        break
                    
                    try:
                        video_data = self._extract_video_data(container)
                        if video_data:
                            video_data['is_liked'] = True
                            likes.append(video_data)
                            likes_collected += 1
                    
                    except Exception as e:
                        logger.warning(f"Error extracting liked video data: {str(e)}")
                        continue
                
                # Scroll for more likes
                if likes_collected < limit:
                    self._scroll_for_more_content()
                    scroll_attempts += 1
                    self.anti_detection.random_delay(2, 4)
            
            logger.info(f"Scraped {len(likes)} liked videos for: {username}")
            return likes
        
        except Exception as e:
            logger.error(f"Error scraping likes: {str(e)}")
            return []
    
    def scrape_feed(self, limit=30):
        """
        Scrape the For You feed
        
        Args:
            limit: Maximum number of videos to scrape
        
        Returns:
            list: List of feed video data
        """
        try:
            feed_videos = []
            
            self.driver.get(self.base_url)
            self.anti_detection.random_delay(3, 5)
            
            videos_collected = 0
            scroll_attempts = 0
            max_scroll_attempts = 20
            
            while videos_collected < limit and scroll_attempts < max_scroll_attempts:
                # Find feed video containers
                video_containers = self.driver.find_elements(
                    By.XPATH, self.selectors['feed_video']
                )
                
                for container in video_containers[videos_collected:]:
                    if videos_collected >= limit:
                        break
                    
                    try:
                        video_data = self._extract_feed_video_data(container)
                        if video_data:
                            feed_videos.append(video_data)
                            videos_collected += 1
                    
                    except Exception as e:
                        logger.warning(f"Error extracting feed video data: {str(e)}")
                        continue
                
                # Scroll for more videos
                if videos_collected < limit:
                    self._scroll_for_more_content()
                    scroll_attempts += 1
                    self.anti_detection.random_delay(2, 4)
            
            logger.info(f"Scraped {len(feed_videos)} videos from feed")
            return feed_videos
        
        except Exception as e:
            logger.error(f"Error scraping feed: {str(e)}")
            return []
    
    def scrape_user_profile(self, username):
        """
        Scrape detailed user profile information
        
        Args:
            username: TikTok username to scrape
        
        Returns:
            dict: User profile data
        """
        try:
            profile_url = self.profile_url.format(username=username)
            
            self.driver.get(profile_url)
            self.anti_detection.random_delay(3, 5)
            
            # Extract profile data
            profile_data = {
                'username': username,
                'display_name': self._safe_get_text('[data-e2e="user-title"]'),
                'bio': self._safe_get_text('[data-e2e="user-bio"]'),
                'follower_count': self._extract_count('[data-e2e="followers-count"]'),
                'following_count': self._extract_count('[data-e2e="following-count"]'),
                'likes_count': self._extract_count('[data-e2e="likes-count"]'),
                'video_count': self._count_videos(),
                'avatar_url': self._safe_get_attribute('[data-e2e="avatar"] img', 'src'),
                'is_verified': self._check_verification(),
                'is_private': self._is_private_account(),
                'scraped_at': datetime.now().isoformat()
            }
            
            logger.info(f"Scraped profile data for: {username}")
            return profile_data
        
        except Exception as e:
            logger.error(f"Error scraping user profile: {str(e)}")
            return {}
    
    def scrape_hashtag(self, hashtag, limit=30):
        """
        Scrape videos from a specific hashtag
        
        Args:
            hashtag: Hashtag to scrape (without #)
            limit: Maximum number of videos to scrape
        
        Returns:
            list: List of hashtag video data
        """
        try:
            hashtag_videos = []
            hashtag_url = f"{self.base_url}/tag/{hashtag}"
            
            self.driver.get(hashtag_url)
            self.anti_detection.random_delay(3, 5)
            
            videos_collected = 0
            scroll_attempts = 0
            max_scroll_attempts = 15
            
            while videos_collected < limit and scroll_attempts < max_scroll_attempts:
                # Find video containers
                video_containers = self.driver.find_elements(
                    By.XPATH, self.selectors['video_container']
                )
                
                for container in video_containers[videos_collected:]:
                    if videos_collected >= limit:
                        break
                    
                    try:
                        video_data = self._extract_video_data(container)
                        if video_data:
                            video_data['hashtag'] = hashtag
                            hashtag_videos.append(video_data)
                            videos_collected += 1
                    
                    except Exception as e:
                        logger.warning(f"Error extracting hashtag video data: {str(e)}")
                        continue
                
                # Scroll for more videos
                if videos_collected < limit:
                    self._scroll_for_more_content()
                    scroll_attempts += 1
                    self.anti_detection.random_delay(2, 4)
            
            logger.info(f"Scraped {len(hashtag_videos)} videos from hashtag: {hashtag}")
            return hashtag_videos
        
        except Exception as e:
            logger.error(f"Error scraping hashtag: {str(e)}")
            return []
    
    def _extract_video_data(self, container):
        """
        Extract video data from a video container element
        
        Args:
            container: Video container WebElement
        
        Returns:
            dict: Video data
        """
        try:
            video_data = {
                'video_id': self._extract_video_id(container),
                'description': self._safe_get_text_from_element(container, self.selectors['video_description']),
                'view_count': self._extract_count_from_element(container, self.selectors['video_stats']),
                'like_count': self._extract_count_from_element(container, self.selectors['like_count']),
                'comment_count': self._extract_count_from_element(container, self.selectors['comment_count']),
                'share_count': self._extract_count_from_element(container, self.selectors['share_count']),
                'author_username': self._extract_author_username(container),
                'video_url': self._extract_video_url(container),
                'thumbnail_url': self._extract_thumbnail_url(container),
                'scraped_at': datetime.now().isoformat()
            }
            
            return video_data
        
        except Exception as e:
            logger.warning(f"Error extracting video data: {str(e)}")
            return None
    
    def _extract_feed_video_data(self, container):
        """
        Extract video data from a feed video container
        
        Args:
            container: Feed video container WebElement
        
        Returns:
            dict: Video data
        """
        try:
            # Feed videos have slightly different structure
            video_data = {
                'video_id': self._extract_video_id(container),
                'description': self._safe_get_text_from_element(container, '[data-e2e="video-desc"]'),
                'author_username': self._extract_author_username(container),
                'video_url': self._extract_video_url(container),
                'is_feed_video': True,
                'scraped_at': datetime.now().isoformat()
            }
            
            return video_data
        
        except Exception as e:
            logger.warning(f"Error extracting feed video data: {str(e)}")
            return None
    
    def _extract_user_data(self, element):
        """
        Extract user data from a user element
        
        Args:
            element: User element WebElement
        
        Returns:
            dict: User data
        """
        try:
            user_data = {
                'username': self._extract_username_from_element(element),
                'display_name': self._safe_get_text_from_element(element, '[data-e2e="user-title"]'),
                'avatar_url': self._safe_get_attribute_from_element(element, 'img', 'src'),
                'follower_count': self._extract_count_from_element(element, '[data-e2e="user-follower-count"]'),
                'is_verified': self._check_verification_from_element(element),
                'scraped_at': datetime.now().isoformat()
            }
            
            return user_data
        
        except Exception as e:
            logger.warning(f"Error extracting user data: {str(e)}")
            return None
    
    def _scroll_for_more_content(self):
        """
        Scroll down to load more content
        """
        try:
            # Scroll to bottom
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # Wait for content to load
            self.anti_detection.random_delay(2, 4)
            
            # Additional scroll with random amount
            scroll_amount = random.randint(500, 1500)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            
        except Exception as e:
            logger.warning(f"Error scrolling for more content: {str(e)}")
    
    def _is_private_or_restricted(self):
        """
        Check if content is private or restricted
        
        Returns:
            bool: True if private/restricted, False otherwise
        """
        try:
            private_indicators = [
                'This user\'s',
                'private',
                'restricted',
                'not available',
                'can\'t view'
            ]
            
            page_text = self.driver.page_source.lower()
            
            for indicator in private_indicators:
                if indicator in page_text:
                    return True
            
            return False
        
        except Exception as e:
            logger.warning(f"Error checking privacy status: {str(e)}")
            return False
    
    def _safe_get_text(self, selector):
        """
        Safely get text from an element
        
        Args:
            selector: CSS selector
        
        Returns:
            str: Element text or empty string
        """
        try:
            element = self.driver.find_element(By.XPATH, selector)
            return element.text.strip()
        except:
            return ""
    
    def _safe_get_text_from_element(self, parent, selector):
        """
        Safely get text from a child element
        
        Args:
            parent: Parent WebElement
            selector: CSS selector for child
        
        Returns:
            str: Element text or empty string
        """
        try:
            element = parent.find_element(By.XPATH, selector)
            return element.text.strip()
        except:
            return ""
    
    def _safe_get_attribute(self, selector, attribute):
        """
        Safely get attribute from an element
        
        Args:
            selector: CSS selector
            attribute: Attribute name
        
        Returns:
            str: Attribute value or empty string
        """
        try:
            element = self.driver.find_element(By.XPATH, selector)
            return element.get_attribute(attribute) or ""
        except:
            return ""
    
    def _safe_get_attribute_from_element(self, parent, selector, attribute):
        """
        Safely get attribute from a child element
        
        Args:
            parent: Parent WebElement
            selector: CSS selector for child
            attribute: Attribute name
        
        Returns:
            str: Attribute value or empty string
        """
        try:
            element = parent.find_element(By.XPATH, selector)
            return element.get_attribute(attribute) or ""
        except:
            return ""
    
    def _extract_count(self, selector):
        """
        Extract and parse count from text
        
        Args:
            selector: CSS selector
        
        Returns:
            int: Parsed count or 0
        """
        try:
            text = self._safe_get_text(selector)
            return self._parse_count(text)
        except:
            return 0
    
    def _extract_count_from_element(self, parent, selector):
        """
        Extract and parse count from child element
        
        Args:
            parent: Parent WebElement
            selector: CSS selector for child
        
        Returns:
            int: Parsed count or 0
        """
        try:
            text = self._safe_get_text_from_element(parent, selector)
            return self._parse_count(text)
        except:
            return 0
    
    def _parse_count(self, text):
        """
        Parse count from text (handles K, M, B suffixes)
        
        Args:
            text: Text containing count
        
        Returns:
            int: Parsed count
        """
        try:
            if not text:
                return 0
            
            # Remove non-numeric characters except K, M, B
            clean_text = re.sub(r'[^0-9KMB.]', '', text.upper())
            
            if 'K' in clean_text:
                number = float(clean_text.replace('K', ''))
                return int(number * 1000)
            elif 'M' in clean_text:
                number = float(clean_text.replace('M', ''))
                return int(number * 1000000)
            elif 'B' in clean_text:
                number = float(clean_text.replace('B', ''))
                return int(number * 1000000000)
            else:
                return int(float(clean_text))
        
        except:
            return 0
    
    def _extract_video_id(self, container):
        """
        Extract video ID from container
        
        Args:
            container: Video container WebElement
        
        Returns:
            str: Video ID or empty string
        """
        try:
            video_link = container.find_element(By.XPATH, './/a[contains(@href, "/video/")]')
            href = video_link.get_attribute('href')
            
            # Extract video ID from URL
            match = re.search(r'/video/([0-9]+)', href)
            if match:
                return match.group(1)
            
            return ""
        except:
            return ""
    
    def _extract_video_url(self, container):
        """
        Extract video URL from container
        
        Args:
            container: Video container WebElement
        
        Returns:
            str: Video URL or empty string
        """
        try:
            video_link = container.find_element(By.XPATH, './/a[contains(@href, "/video/")]')
            return video_link.get_attribute('href') or ""
        except:
            return ""
    
    def _extract_thumbnail_url(self, container):
        """
        Extract thumbnail URL from container
        
        Args:
            container: Video container WebElement
        
        Returns:
            str: Thumbnail URL or empty string
        """
        try:
            img_element = container.find_element(By.XPATH, './/img')
            return img_element.get_attribute('src') or ""
        except:
            return ""
    
    def _extract_author_username(self, container):
        """
        Extract author username from container
        
        Args:
            container: Video container WebElement
        
        Returns:
            str: Author username or empty string
        """
        try:
            # Try different XPath selectors for username
            xpath_selectors = [
                './/*[@data-e2e="user-title"]',
                './/a[contains(@href, "/@")]',
                './/*[contains(@class, "author-username")]'
            ]

            for selector in xpath_selectors:
                try:
                    element = container.find_element(By.XPATH, selector)
                    text = element.text.strip()
                    if text:
                        # Clean username
                        username = text.replace('@', '')
                        return username
                except:
                    continue
            
            return ""
        except:
            return ""
    
    def _extract_username_from_element(self, element):
        """
        Extract username from user element
        
        Args:
            element: User element WebElement
        
        Returns:
            str: Username or empty string
        """
        try:
            # Try to find username link
            username_link = element.find_element(By.XPATH, './/a[contains(@href, "/@")]')
            href = username_link.get_attribute('href')
            
            # Extract username from URL
            match = re.search(r'/@([^/?]+)', href)
            if match:
                return match.group(1)
            
            return ""
        except:
            return ""
    
    def _count_videos(self):
        """
        Count total videos on profile
        
        Returns:
            int: Video count
        """
        try:
            video_containers = self.driver.find_elements(
                By.XPATH, self.selectors['video_container']
            )
            return len(video_containers)
        except:
            return 0
    
    def _check_verification(self):
        """
        Check if user is verified
        
        Returns:
            bool: True if verified, False otherwise
        """
        try:
            verification_xpath_selectors = [
                '//*[@data-e2e="verified-icon"]',
                '//*[contains(@class, "verified-icon")]',
                '//svg[@data-e2e="verified-icon"]'
            ]

            for selector in verification_xpath_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        return True
                except:
                    continue
            
            return False
        except:
            return False
    
    def _check_verification_from_element(self, parent):
        """
        Check if user is verified from parent element
        
        Args:
            parent: Parent WebElement
        
        Returns:
            bool: True if verified, False otherwise
        """
        try:
            verification_xpath_selectors = [
                './/*[@data-e2e="verified-icon"]',
                './/*[contains(@class, "verified-icon")]',
                './/svg[@data-e2e="verified-icon"]'
            ]

            for selector in verification_xpath_selectors:
                try:
                    element = parent.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        return True
                except:
                    continue
            
            return False
        except:
            return False
    
    def _is_private_account(self):
        """
        Check if the current profile is private
        
        Returns:
            bool: True if private, False otherwise
        """
        try:
            private_indicators = [
                'This account is private',
                'private account',
                'Follow to see'
            ]
            
            page_text = self.driver.page_source
            
            for indicator in private_indicators:
                if indicator in page_text:
                    return True
            
            return False
        except:
            return False