#!/usr/bin/env python3
"""
TikTok Authenticator

A focused, working TikTok login implementation for the Actor system.
Designed to work with real TikTok accounts using Selenium WebDriver.
"""

import time
import random
import logging
from typing import Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class TikTokAuthenticator:
    """
    TikTok authenticator for the Actor system that uses real authentication
    """
    
    def __init__(self):
        """Initialize TikTok authenticator"""
        self.base_url = "https://www.tiktok.com"
        self.login_url = "https://www.tiktok.com/login/phone-or-email/email"
    
    def login(self, username: str, password: str) -> Dict:
        """
        Authenticate with <PERSON>ikTok using real credentials
        
        Args:
            username: TikTok username or email
            password: TikTok password
            
        Returns:
            Dict with success status and session info
        """
        logger.info(f"🚀 Starting TikTok login for: {username}")
        
        driver = None
        try:
            # Setup standard Chrome driver with anti-detection
            options = Options()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-extensions")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Create driver with WebDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Navigate to login page
            logger.info("📱 Navigating to TikTok login page...")
            driver.get(self.login_url)
            time.sleep(random.uniform(3, 5))
            
            # Wait for and fill email field with multiple selector attempts
            logger.info("✍️ Filling login credentials...")
            email_field = None

            # Try multiple selectors for username field
            username_selectors = [
                (By.NAME, "username"),
                (By.CSS_SELECTOR, "input[placeholder*='email']"),
                (By.CSS_SELECTOR, "input[placeholder*='Email']"),
                (By.CSS_SELECTOR, "input[type='text']"),
                (By.CSS_SELECTOR, "input[autocomplete='username']"),
                (By.XPATH, "//input[contains(@placeholder, 'email') or contains(@placeholder, 'Email')]")
            ]

            for selector_type, selector_value in username_selectors:
                try:
                    email_field = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((selector_type, selector_value))
                    )
                    logger.info(f"✅ Found username field with selector: {selector_type} = {selector_value}")
                    break
                except:
                    continue

            if not email_field:
                logger.error("❌ Could not find username field")
                return {'success': False, 'error': 'Username field not found'}

            # Human-like typing for username
            for char in username:
                email_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            time.sleep(random.uniform(1, 2))

            # Fill password field with multiple selector attempts
            password_field = None
            password_selectors = [
                (By.NAME, "password"),
                (By.CSS_SELECTOR, "input[type='password']"),
                (By.CSS_SELECTOR, "input[placeholder*='password']"),
                (By.CSS_SELECTOR, "input[placeholder*='Password']"),
                (By.XPATH, "//input[@type='password']")
            ]

            for selector_type, selector_value in password_selectors:
                try:
                    password_field = driver.find_element(selector_type, selector_value)
                    logger.info(f"✅ Found password field with selector: {selector_type} = {selector_value}")
                    break
                except:
                    continue

            if not password_field:
                logger.error("❌ Could not find password field")
                return {'success': False, 'error': 'Password field not found'}

            # Human-like typing for password
            for char in password:
                password_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            time.sleep(random.uniform(1, 2))

            # Click login button with multiple selector attempts
            logger.info("🔐 Submitting login...")
            login_button = None
            login_selectors = [
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.CSS_SELECTOR, "button[data-e2e='login-button']"),
                (By.CSS_SELECTOR, "div[role='button']"),
                (By.XPATH, "//button[contains(text(), 'Log in') or contains(text(), 'Login')]"),
                (By.XPATH, "//div[contains(text(), 'Log in') or contains(text(), 'Login')]")
            ]

            for selector_type, selector_value in login_selectors:
                try:
                    login_button = driver.find_element(selector_type, selector_value)
                    logger.info(f"✅ Found login button with selector: {selector_type} = {selector_value}")
                    break
                except:
                    continue

            if not login_button:
                logger.error("❌ Could not find login button")
                return {'success': False, 'error': 'Login button not found'}

            login_button.click()
            
            # Wait for login result
            time.sleep(5)
            current_url = driver.current_url
            logger.info(f"Current URL after login: {current_url}")
            
            # Check for success indicators
            if self._check_login_success(driver):
                logger.info("✅ Login successful!")
                
                # Get session data
                cookies = driver.get_cookies()
                session_info = {
                    'cookies': cookies,
                    'user_agent': driver.execute_script("return navigator.userAgent;"),
                    'current_url': current_url
                }
                
                return {
                    'success': True,
                    'session_info': session_info,
                    'message': 'Login successful'
                }
            else:
                # Check for error messages
                error_msg = self._get_error_message(driver)
                logger.warning(f"❌ Login failed: {error_msg}")
                
                return {
                    'success': False,
                    'error': error_msg or 'Login failed - unknown reason',
                    'current_url': current_url
                }
                
        except Exception as e:
            logger.error(f"❌ Login failed with exception: {str(e)}")
            return {'success': False, 'error': str(e)}
        
        finally:
            if driver:
                driver.quit()
    
    def _check_login_success(self, driver) -> bool:
        """Check if login was successful"""
        try:
            # Check if we're redirected to main page or profile
            current_url = driver.current_url
            success_indicators = [
                "tiktok.com/foryou",
                "tiktok.com/following",
                "tiktok.com/@",
                not "login" in current_url.lower()
            ]
            
            return any(success_indicators)
            
        except Exception as e:
            logger.error(f"Error checking login success: {str(e)}")
            return False
    
    def _get_error_message(self, driver) -> str:
        """Extract error message from login page"""
        try:
            error_selectors = [
                ".error-message",
                "[data-e2e='error-message']",
                ".login-error",
                ".form-error"
            ]
            
            for selector in error_selectors:
                try:
                    error_element = driver.find_element(By.CSS_SELECTOR, selector)
                    if error_element.is_displayed():
                        return error_element.text
                except NoSuchElementException:
                    continue
            
            return "Login failed - no specific error message found"
            
        except Exception as e:
            logger.error(f"Error getting error message: {str(e)}")
            return "Login failed - error checking failed"
