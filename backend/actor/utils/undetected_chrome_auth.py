import time
import random
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent

logger = logging.getLogger(__name__)

class StandardChromeAuthenticator:
    """
    TikTok authentication using standard Selenium WebDriver with anti-detection measures
    """
    
    def __init__(self):
        self.ua = UserAgent()
        self.viewport_sizes = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024)
        ]
        self.login_url = "https://www.tiktok.com/login"
        self.mobile_login_url = "https://www.tiktok.com/login?enter_from=mobile_main"
        self.alternative_login_url = "https://www.tiktok.com/login/phone-or-email"
    
    def setup_driver(self, headless=False, proxy=None):
        """
        Setup standard Chrome driver with anti-detection measures

        Args:
            headless: Whether to run in headless mode
            proxy: Proxy configuration if needed

        Returns:
            WebDriver instance
        """
        try:
            # Random viewport size for more human-like behavior
            viewport = random.choice(self.viewport_sizes)

            # Configure Chrome options
            options = Options()

            # Set user agent
            user_agent = self.ua.random
            options.add_argument(f'--user-agent={user_agent}')

            # Set window size
            options.add_argument(f'--window-size={viewport[0]},{viewport[1]}')

            # Anti-detection options
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Proxy configuration
            if proxy:
                options.add_argument(f'--proxy-server={proxy}')

            # Headless mode
            if headless:
                options.add_argument('--headless')

            # Create standard Chrome driver with WebDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)

            # Execute stealth script
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Set random geolocation
            self._set_random_geolocation(driver)

            logger.info(f"Standard Chrome driver setup complete with user agent: {user_agent}")
            return driver

        except Exception as e:
            logger.error(f"Error setting up Chrome driver: {str(e)}")
            raise
    
    def _set_random_geolocation(self, driver):
        """
        Set a random geolocation to appear more natural
        """
        try:
            # Random coordinates (major cities)
            locations = [
                {'latitude': 40.7128, 'longitude': -74.0060, 'accuracy': 100},  # New York
                {'latitude': 34.0522, 'longitude': -118.2437, 'accuracy': 100}, # Los Angeles
                {'latitude': 51.5074, 'longitude': -0.1278, 'accuracy': 100},   # London
                {'latitude': 48.8566, 'longitude': 2.3522, 'accuracy': 100},    # Paris
                {'latitude': 35.6762, 'longitude': 139.6503, 'accuracy': 100},  # Tokyo
            ]
            
            location = random.choice(locations)
            driver.execute_cdp_cmd('Emulation.setGeolocationOverride', location)
            
            logger.info(f"Set geolocation to: {location['latitude']}, {location['longitude']}")
        
        except Exception as e:
            logger.warning(f"Error setting geolocation: {str(e)}")
    
    def human_like_type(self, element, text):
        """
        Type text in a human-like manner with variable speed and occasional mistakes
        
        Args:
            element: WebElement to type into
            text: Text to type
        """
        try:
            for char in text:
                # Random typing speed
                typing_speed = random.uniform(0.05, 0.2)
                
                # Occasionally make a typo and correct it (5% chance)
                if random.random() < 0.05:
                    # Type a random wrong character
                    wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                    element.send_keys(wrong_char)
                    time.sleep(random.uniform(0.1, 0.3))
                    
                    # Delete the wrong character
                    element.send_keys(Keys.BACKSPACE)
                    time.sleep(random.uniform(0.1, 0.2))
                
                # Type the correct character
                element.send_keys(char)
                time.sleep(typing_speed)
                
                # Occasionally pause while typing (2% chance)
                if random.random() < 0.02:
                    time.sleep(random.uniform(0.3, 0.7))
            
            # Pause after completing typing
            time.sleep(random.uniform(0.3, 0.7))
        
        except Exception as e:
            logger.warning(f"Error during human-like typing: {str(e)}")
            # Fallback to regular typing
            element.clear()
            element.send_keys(text)
    
    def human_like_click(self, driver, element):
        """
        Perform human-like click with random delays and movements
        
        Args:
            driver: WebDriver instance
            element: Element to click
        """
        try:
            # Scroll element into view with a slight offset
            driver.execute_script(
                "arguments[0].scrollIntoView({block: 'center', inline: 'center'});", 
                element
            )
            time.sleep(random.uniform(0.3, 0.7))
            
            # Move to element with slight randomness using JavaScript
            # This is more reliable than ActionChains with undetected-chromedriver
            driver.execute_script(
                "arguments[0].dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));", 
                element
            )
            time.sleep(random.uniform(0.2, 0.5))
            
            # Click the element
            element.click()
            
            # Random delay after click
            time.sleep(random.uniform(0.5, 1.2))
            
            logger.debug("Performed human-like click")
        
        except Exception as e:
            logger.warning(f"Error during human-like click, falling back to regular click: {str(e)}")
            element.click()
            time.sleep(random.uniform(0.3, 0.7))
    
    def wait_for_element_safely(self, driver, by, value, timeout=10, condition="presence"):
        """
        Safely wait for an element with proper error handling
        
        Args:
            driver: WebDriver instance
            by: Locator type (By.ID, By.CSS_SELECTOR, etc.)
            value: Locator value
            timeout: Maximum wait time in seconds
            condition: Wait condition (presence, visibility, clickable)
        
        Returns:
            WebElement if found, None otherwise
        """
        try:
            if condition == "presence":
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located((by, value))
                )
            elif condition == "visibility":
                element = WebDriverWait(driver, timeout).until(
                    EC.visibility_of_element_located((by, value))
                )
            elif condition == "clickable":
                element = WebDriverWait(driver, timeout).until(
                    EC.element_to_be_clickable((by, value))
                )
            else:
                logger.warning(f"Unknown wait condition: {condition}, defaulting to presence")
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located((by, value))
                )
            
            return element
        
        except TimeoutException:
            logger.warning(f"Timeout waiting for element: {value} with condition: {condition}")
            return None
        except Exception as e:
            logger.warning(f"Error waiting for element: {value}, error: {str(e)}")
            return None
    
    def simulate_human_behavior(self, driver):
        """
        Simulate human-like behavior patterns
        
        Args:
            driver: WebDriver instance
        """
        try:
            # Get window size for realistic movements
            window_size = driver.get_window_size()
            max_x = window_size['width'] - 100
            max_y = window_size['height'] - 100
            
            # Random mouse movements using JavaScript
            # More reliable than ActionChains with undetected-chromedriver
            for _ in range(random.randint(2, 5)):
                x = random.randint(50, max_x)
                y = random.randint(50, max_y)
                
                driver.execute_script(
                    f"document.elementFromPoint({x}, {y})?.dispatchEvent(new MouseEvent('mouseover', {{bubbles: true}}));"
                )
                time.sleep(random.uniform(0.2, 0.5))
            
            # Simulate realistic scrolling behavior
            scroll_amount = random.randint(100, 300)
            driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            time.sleep(random.uniform(0.5, 1.5))
            
            # Random pause to simulate thinking/reading
            time.sleep(random.uniform(1.0, 2.5))
            
            logger.debug("Simulated human behavior")
        
        except Exception as e:
            logger.warning(f"Error simulating human behavior: {str(e)}")
    
    def handle_cookie_consent(self, driver):
        """
        Handle cookie consent dialogs if present
        
        Args:
            driver: WebDriver instance
        """
        try:
            # Common cookie consent button selectors
            cookie_selectors = [
                "button[data-e2e='cookie-banner-accept']",
                "button.accept-cookies",
                "button.cookie-accept",
                "button.cookie-consent-accept",
                "button.accept-all-cookies",
                "#onetrust-accept-btn-handler",
                ".cookie-banner button[type='submit']",
                ".cookie-notice-container .accept"
            ]
            
            for selector in cookie_selectors:
                try:
                    cookie_button = self.wait_for_element_safely(
                        driver, By.CSS_SELECTOR, selector, timeout=3, condition="clickable"
                    )
                    if cookie_button:
                        self.human_like_click(driver, cookie_button)
                        logger.info(f"Accepted cookies using selector: {selector}")
                        time.sleep(random.uniform(1.0, 2.0))
                        return True
                except Exception:
                    continue
            
            logger.info("No cookie consent dialog found or needed")
            return False
        
        except Exception as e:
            logger.warning(f"Error handling cookie consent: {str(e)}")
            return False
    
    def handle_app_popup(self, driver):
        """
        Handle app download popups if present
        
        Args:
            driver: WebDriver instance
        """
        try:
            # Common app popup close button selectors
            popup_selectors = [
                "button[data-e2e='modal-close']",
                ".modal-close-button",
                ".close-button",
                ".app-download-close",
                ".modal .close",
                ".modal .dismiss"
            ]
            
            for selector in popup_selectors:
                try:
                    close_button = self.wait_for_element_safely(
                        driver, By.CSS_SELECTOR, selector, timeout=3, condition="clickable"
                    )
                    if close_button:
                        self.human_like_click(driver, close_button)
                        logger.info(f"Closed app popup using selector: {selector}")
                        time.sleep(random.uniform(1.0, 2.0))
                        return True
                except Exception:
                    continue
            
            logger.info("No app popup found or needed")
            return False
        
        except Exception as e:
            logger.warning(f"Error handling app popup: {str(e)}")
            return False
    
    def login(self, driver, username, password, use_2fa=False, two_factor_code=None, retry_count=0):
        """
        Perform TikTok login using undetected-chromedriver
        
        Args:
            driver: Undetected Chrome WebDriver instance
            username: TikTok username or email
            password: TikTok password
            use_2fa: Whether to use 2FA
            two_factor_code: 2FA code if available
            retry_count: Current retry attempt number
        
        Returns:
            dict: Login result with success status and session data
        """
        try:
            logger.info(f"Starting TikTok login with undetected Chrome for username: {username} (attempt {retry_count + 1})")
            
            # Implement progressive delays for retries
            if retry_count > 0:
                delay = min(30 * (1.5 ** (retry_count - 1)), 180)  # Max 3 minutes
                logger.info(f"Waiting {delay:.1f} seconds before retry {retry_count}")
                time.sleep(delay)
            
            # Try multiple login URLs
            login_urls = [
                self.login_url,
                self.alternative_login_url,
                self.mobile_login_url
            ]
            
            # Shuffle URLs for randomness
            random.shuffle(login_urls)
            
            # Try each URL until successful
            for url_index, login_url in enumerate(login_urls):
                try:
                    logger.info(f"Attempting login URL {url_index + 1}/{len(login_urls)}: {login_url}")
                    
                    # Navigate to login page
                    driver.get(login_url)
                    time.sleep(random.uniform(3.0, 5.0))  # Wait for page to load
                    
                    # Simulate human behavior
                    self.simulate_human_behavior(driver)
                    
                    # Handle cookie consent if present
                    self.handle_cookie_consent(driver)
                    
                    # Handle app popup if present
                    self.handle_app_popup(driver)
                    
                    # Select login method (email/username) if needed
                    login_method_selectors = [
                        "a[href*='phone-or-email']",
                        "a[data-e2e='email-login-link']",
                        "button[data-e2e='email-login-button']",
                        ".login-button-container button",
                        "button:contains('Use phone / email / username')"
                    ]
                    
                    for selector in login_method_selectors:
                        try:
                            login_method = self.wait_for_element_safely(
                                driver, By.CSS_SELECTOR, selector, timeout=3, condition="clickable"
                            )
                            if login_method:
                                self.human_like_click(driver, login_method)
                                logger.info(f"Selected login method using selector: {selector}")
                                time.sleep(random.uniform(2.0, 3.0))
                                break
                        except Exception:
                            continue
                    
                    # Find username field
                    username_selectors = [
                        "input[type='text'][placeholder='Email or username']",
                        "input[name='username'][type='text']",
                        "input[placeholder='Email or username']",
                        "input[data-e2e='email-username-field']"
                    ]
                    
                    username_field = None
                    for selector in username_selectors:
                        username_field = self.wait_for_element_safely(
                            driver, By.CSS_SELECTOR, selector, timeout=5, condition="visibility"
                        )
                        if username_field:
                            logger.info(f"Found username field with selector: {selector}")
                            break
                    
                    if not username_field:
                        logger.error("Username field not found with any selector")
                        continue  # Try next URL
                    
                    # Find password field
                    password_selectors = [
                        "input[type='password'][placeholder='Password']",
                        "input[name='password'][type='password']",
                        "input[placeholder='Password']",
                        "input[data-e2e='password-field']"
                    ]
                    
                    password_field = None
                    for selector in password_selectors:
                        password_field = self.wait_for_element_safely(
                            driver, By.CSS_SELECTOR, selector, timeout=5, condition="visibility"
                        )
                        if password_field:
                            logger.info(f"Found password field with selector: {selector}")
                            break
                    
                    if not password_field:
                        logger.error("Password field not found with any selector")
                        continue  # Try next URL
                    
                    # Enter username
                    username_field.clear()
                    self.human_like_type(username_field, username)
                    time.sleep(random.uniform(0.8, 1.5))
                    
                    # Enter password
                    password_field.clear()
                    self.human_like_type(password_field, password)
                    time.sleep(random.uniform(0.8, 1.5))
                    
                    # Find and click login button
                    login_button_selectors = [
                        "button[type='submit']",
                        "button[data-e2e='login-button']",
                        ".login-button",
                        "button.login-button",
                        "button:contains('Log in')"
                    ]
                    
                    login_button = None
                    for selector in login_button_selectors:
                        login_button = self.wait_for_element_safely(
                            driver, By.CSS_SELECTOR, selector, timeout=5, condition="clickable"
                        )
                        if login_button:
                            logger.info(f"Found login button with selector: {selector}")
                            break
                    
                    if not login_button:
                        logger.error("Login button not found with any selector")
                        continue  # Try next URL
                    
                    # Click login button
                    self.human_like_click(driver, login_button)
                    time.sleep(random.uniform(3.0, 5.0))  # Wait for login process
                    
                    # Check for CAPTCHA
                    captcha_selectors = [
                        ".captcha-container",
                        "iframe[title*='captcha']",
                        "iframe[src*='captcha']",
                        ".tiktok-captcha",
                        "div[data-e2e='captcha-container']"
                    ]
                    
                    for selector in captcha_selectors:
                        captcha = self.wait_for_element_safely(
                            driver, By.CSS_SELECTOR, selector, timeout=3, condition="presence"
                        )
                        if captcha:
                            logger.warning("CAPTCHA detected, waiting for manual resolution")
                            # Wait longer for manual CAPTCHA resolution
                            time.sleep(30)  # Give user time to solve CAPTCHA manually
                            break
                    
                    # Handle 2FA if required
                    if use_2fa or self._is_2fa_required(driver):
                        if not two_factor_code:
                            return {
                                'success': False,
                                'error': '2FA code required but not provided',
                                'requires_2fa': True
                            }
                        
                        tfa_result = self._handle_2fa(driver, two_factor_code)
                        if not tfa_result['success']:
                            return tfa_result
                    
                    # Verify successful login
                    login_verification = self._verify_login_success(driver)
                    if login_verification['success']:
                        # Extract session data
                        session_data = self._extract_session_data(driver)
                        
                        return {
                            'success': True,
                            'session_data': session_data,
                            'session_metadata': {
                                'login_timestamp': time.time(),
                                'user_agent': driver.execute_script("return navigator.userAgent;"),
                                'viewport_size': driver.get_window_size(),
                                'retry_count': retry_count,
                                'login_url': login_url
                            }
                        }
                    else:
                        logger.warning(f"Login verification failed: {login_verification.get('error', 'Unknown error')}")
                        # Try next URL if this one failed
                        continue
                    
                except Exception as e:
                    logger.error(f"Error during login with URL {login_url}: {str(e)}")
                    continue  # Try next URL
            
            # All URLs failed
            return {
                'success': False,
                'error': f'All {len(login_urls)} login URLs failed',
                'urls_exhausted': True
            }
        
        except Exception as e:
            logger.error(f"Error during TikTok login: {str(e)}")
            return {
                'success': False,
                'error': f'Login failed: {str(e)}'
            }
    
    def _is_2fa_required(self, driver):
        """
        Check if 2FA is required
        
        Args:
            driver: WebDriver instance
        
        Returns:
            bool: True if 2FA is required, False otherwise
        """
        try:
            # Common 2FA indicators
            tfa_selectors = [
                ".verification-code",
                "input[placeholder*='verification']",
                "input[placeholder*='code']",
                "div:contains('verification code')",
                "div:contains('2-step verification')",
                "div[data-e2e='verification-code-input']"
            ]
            
            for selector in tfa_selectors:
                try:
                    tfa_element = self.wait_for_element_safely(
                        driver, By.CSS_SELECTOR, selector, timeout=2, condition="presence"
                    )
                    if tfa_element:
                        logger.info(f"2FA required, found indicator with selector: {selector}")
                        return True
                except Exception:
                    continue
            
            return False
        
        except Exception as e:
            logger.warning(f"Error checking for 2FA: {str(e)}")
            return False
    
    def _handle_2fa(self, driver, two_factor_code):
        """
        Handle 2FA verification
        
        Args:
            driver: WebDriver instance
            two_factor_code: 2FA code
        
        Returns:
            dict: Result with success status
        """
        try:
            # Find 2FA input field
            tfa_input_selectors = [
                "input[placeholder*='verification']",
                "input[placeholder*='code']",
                "input[data-e2e='verification-code-input']",
                ".verification-code input"
            ]
            
            tfa_input = None
            for selector in tfa_input_selectors:
                tfa_input = self.wait_for_element_safely(
                    driver, By.CSS_SELECTOR, selector, timeout=5, condition="visibility"
                )
                if tfa_input:
                    logger.info(f"Found 2FA input field with selector: {selector}")
                    break
            
            if not tfa_input:
                return {'success': False, 'error': 'Could not find 2FA input field'}
            
            # Enter 2FA code
            tfa_input.clear()
            self.human_like_type(tfa_input, two_factor_code)
            time.sleep(random.uniform(1.0, 2.0))
            
            # Find and click submit button
            submit_button_selectors = [
                "button[type='submit']",
                "button.submit",
                "button:contains('Submit')",
                "button:contains('Verify')",
                "button.verification-submit"
            ]
            
            submit_button = None
            for selector in submit_button_selectors:
                submit_button = self.wait_for_element_safely(
                    driver, By.CSS_SELECTOR, selector, timeout=5, condition="clickable"
                )
                if submit_button:
                    logger.info(f"Found 2FA submit button with selector: {selector}")
                    break
            
            if not submit_button:
                # Try pressing Enter key instead
                tfa_input.send_keys(Keys.RETURN)
                logger.info("No 2FA submit button found, pressed Enter key instead")
            else:
                # Click submit button
                self.human_like_click(driver, submit_button)
            
            # Wait for verification
            time.sleep(random.uniform(3.0, 5.0))
            
            # Check for error messages
            error_selectors = [
                ".error-message",
                ".error-text",
                "div[data-e2e='error-message']",
                ".verification-error"
            ]
            
            for selector in error_selectors:
                error = self.wait_for_element_safely(
                    driver, By.CSS_SELECTOR, selector, timeout=2, condition="visibility"
                )
                if error and error.is_displayed() and error.text.strip():
                    return {'success': False, 'error': f'2FA verification failed: {error.text}'}
            
            return {'success': True}
        
        except Exception as e:
            logger.error(f"Error handling 2FA: {str(e)}")
            return {'success': False, 'error': f'2FA handling failed: {str(e)}'}
    
    def _verify_login_success(self, driver):
        """
        Verify if login was successful
        
        Args:
            driver: WebDriver instance
        
        Returns:
            dict: Result with success status
        """
        try:
            # Wait for page to load after login attempt
            time.sleep(random.uniform(3.0, 5.0))
            
            # Check current URL for success indicators
            current_url = driver.current_url.lower()
            
            # Success URL patterns
            success_patterns = [
                "tiktok.com/foryou",
                "tiktok.com/following",
                "tiktok.com/explore",
                "tiktok.com/feed",
                "tiktok.com/@",
                "tiktok.com/discover"
            ]
            
            # Failure URL patterns
            failure_patterns = [
                "tiktok.com/login",
                "login",
                "signin",
                "captcha",
                "verification"
            ]
            
            # Check URL patterns
            for pattern in success_patterns:
                if pattern in current_url:
                    logger.info(f"Login success detected via URL pattern: {pattern}")
                    return {'success': True}
            
            for pattern in failure_patterns:
                if pattern in current_url:
                    logger.warning(f"Login failure detected via URL pattern: {pattern}")
                    return {'success': False, 'error': f'Still on login page: {current_url}'}
            
            # Check for success indicators in page content
            success_selectors = [
                ".avatar-wrapper",  # Avatar usually indicates logged in state
                ".user-info",
                ".profile-icon",
                "[data-e2e='profile-icon']",
                "[data-e2e='message-icon']",  # Message icon usually only visible when logged in
                "[data-e2e='upload-icon']",  # Upload icon usually only visible when logged in
                ".upload-icon",
                ".inbox-icon"
            ]
            
            for selector in success_selectors:
                element = self.wait_for_element_safely(
                    driver, By.CSS_SELECTOR, selector, timeout=3, condition="presence"
                )
                if element and element.is_displayed():
                    logger.info(f"Login success detected via element: {selector}")
                    return {'success': True}
            
            # Check for error messages
            error_selectors = [
                ".error-container",
                ".login-error",
                ".error-message",
                "div[data-e2e='login-error']",
                ".captcha-container"
            ]
            
            for selector in error_selectors:
                error = self.wait_for_element_safely(
                    driver, By.CSS_SELECTOR, selector, timeout=2, condition="visibility"
                )
                if error and error.is_displayed() and error.text.strip():
                    error_text = error.text.strip()
                    logger.warning(f"Login error detected: {error_text}")
                    
                    # Check for specific error types
                    if any(keyword in error_text.lower() for keyword in ['incorrect', 'invalid', 'wrong']):
                        return {'success': False, 'error': f'Invalid credentials: {error_text}'}
                    elif any(keyword in error_text.lower() for keyword in ['captcha', 'robot', 'human']):
                        return {'success': False, 'error': f'CAPTCHA required: {error_text}'}
                    elif any(keyword in error_text.lower() for keyword in ['blocked', 'suspicious', 'unusual']):
                        return {'success': False, 'error': f'Account blocked or suspicious activity: {error_text}'}
                    else:
                        return {'success': False, 'error': f'Login error: {error_text}'}
            
            # Check if login form is still present
            login_form_selectors = [
                "form[data-e2e='login-form']",
                "input[name='username']",
                "input[type='password']",
                "button[data-e2e='login-button']"
            ]
            
            for selector in login_form_selectors:
                form_element = self.wait_for_element_safely(
                    driver, By.CSS_SELECTOR, selector, timeout=2, condition="presence"
                )
                if form_element and form_element.is_displayed():
                    logger.warning(f"Login form still present: {selector}")
                    return {'success': False, 'error': 'Login form still present, login failed'}
            
            # If we can't definitively determine success or failure, assume success
            logger.info("Could not definitively determine login status, assuming success")
            return {'success': True}
        
        except Exception as e:
            logger.error(f"Error verifying login success: {str(e)}")
            return {'success': False, 'error': f'Login verification failed: {str(e)}'}
    
    def _extract_session_data(self, driver):
        """
        Extract session data after successful login
        
        Args:
            driver: WebDriver instance
        
        Returns:
            dict: Session data including cookies and local storage
        """
        try:
            # Extract cookies
            cookies = driver.get_cookies()
            
            # Extract local storage
            local_storage = driver.execute_script("""
                let items = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    items[key] = localStorage.getItem(key);
                }
                return items;
            """)
            
            # Extract session storage
            session_storage = driver.execute_script("""
                let items = {};
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    items[key] = sessionStorage.getItem(key);
                }
                return items;
            """)
            
            return {
                'cookies': cookies,
                'local_storage': local_storage,
                'session_storage': session_storage,
                'user_agent': driver.execute_script("return navigator.userAgent;"),
                'timestamp': time.time()
            }
        
        except Exception as e:
            logger.error(f"Error extracting session data: {str(e)}")
            return {
                'cookies': driver.get_cookies(),
                'timestamp': time.time()
            }