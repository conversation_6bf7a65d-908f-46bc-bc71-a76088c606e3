"""
Twitter Authentication Utility

This module provides Twitter authentication interface for the Actor system,
handling login, session management, and real authentication with Twitter/X.
"""

import logging
import json
import time
import random
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class TwitterAuthenticator:
    """
    Twitter authenticator for the Actor system with real authentication.
    
    Handles Twitter/X login, session management, and authentication
    operations using Selenium WebDriver for real browser automation.
    """
    
    def __init__(self):
        self.logger = logger
        self.base_url = "https://twitter.com"
        self.login_url = "https://twitter.com/i/flow/login"
        
    def login(self, username: str, password: str, email: str = None, **kwargs) -> Dict[str, Any]:
        """
        Login method for backward compatibility with enhanced scraper.
        """
        return self.authenticate(username, password, email, **kwargs)

    def authenticate(self, username: str, password: str, email: str = None, **kwargs) -> Dict[str, Any]:
        """
        Authenticate with Twitter using username and password.
        
        Args:
            username: Twitter username
            password: Twitter password
            email: Twitter email (for verification if needed)
            **kwargs: Additional authentication parameters
            
        Returns:
            Authentication result dictionary
        """
        try:
            self.logger.info(f"Starting Twitter authentication for {username}")
            
            driver = None
            try:
                # Setup standard Chrome driver with anti-detection
                options = Options()
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_argument("--disable-extensions")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)

                # Create driver with WebDriverManager
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                
                # Navigate to login page
                self.logger.info("📱 Navigating to Twitter login page...")
                driver.get(self.login_url)
                time.sleep(random.uniform(3, 5))
                
                # Wait for and fill username field with multiple selector attempts
                self.logger.info("✍️ Filling login credentials...")
                username_field = None

                # Try multiple selectors for username field
                username_selectors = [
                    (By.CSS_SELECTOR, "input[autocomplete='username']"),
                    (By.CSS_SELECTOR, "input[name='text']"),
                    (By.CSS_SELECTOR, "input[data-testid='ocfEnterTextTextInput']"),
                    (By.XPATH, "//input[@autocomplete='username']"),
                    (By.XPATH, "//input[contains(@placeholder, 'username') or contains(@placeholder, 'email')]")
                ]

                for selector_type, selector_value in username_selectors:
                    try:
                        username_field = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((selector_type, selector_value))
                        )
                        self.logger.info(f"✅ Found username field with selector: {selector_type} = {selector_value}")
                        break
                    except:
                        continue

                if not username_field:
                    self.logger.error("❌ Could not find username field")
                    return {'success': False, 'error': 'Username field not found'}

                # Human-like typing for username
                for char in username:
                    username_field.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))

                time.sleep(random.uniform(1, 2))

                # Click Next button with multiple selector attempts
                next_button = None
                next_selectors = [
                    (By.CSS_SELECTOR, "div[role='button'][data-testid='LoginForm_Login_Button']"),
                    (By.CSS_SELECTOR, "div[data-testid='LoginForm_Login_Button']"),
                    (By.XPATH, "//div[@role='button' and contains(text(), 'Next')]"),
                    (By.XPATH, "//button[contains(text(), 'Next')]"),
                    (By.CSS_SELECTOR, "button[type='button']")
                ]

                for selector_type, selector_value in next_selectors:
                    try:
                        next_button = driver.find_element(selector_type, selector_value)
                        self.logger.info(f"✅ Found next button with selector: {selector_type} = {selector_value}")
                        break
                    except:
                        continue

                if next_button:
                    next_button.click()
                    time.sleep(random.uniform(2, 4))
                else:
                    self.logger.warning("⚠️ Could not find next button, continuing...")
                
                # Handle potential email verification step
                try:
                    email_field = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "input[data-testid='ocfEnterTextTextInput']"))
                    )
                    if email and email_field.is_displayed():
                        self.logger.info("📧 Email verification required, filling email...")
                        for char in email:
                            email_field.send_keys(char)
                            time.sleep(random.uniform(0.05, 0.15))
                        
                        # Click Next after email
                        next_button = driver.find_element(By.CSS_SELECTOR, "div[data-testid='ocfEnterTextNextButton']")
                        next_button.click()
                        time.sleep(random.uniform(2, 4))
                except TimeoutException:
                    # No email verification needed
                    pass
                
                # Wait longer for password field to appear after clicking Next
                time.sleep(random.uniform(3, 5))

                # Fill password field with multiple selector attempts and longer wait
                password_field = None
                password_selectors = [
                    (By.CSS_SELECTOR, "input[name='password']"),
                    (By.CSS_SELECTOR, "input[type='password']"),
                    (By.CSS_SELECTOR, "input[autocomplete='current-password']"),
                    (By.XPATH, "//input[@name='password']"),
                    (By.XPATH, "//input[@type='password']"),
                    (By.XPATH, "//input[contains(@placeholder, 'Password')]")
                ]

                for selector_type, selector_value in password_selectors:
                    try:
                        password_field = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((selector_type, selector_value))
                        )
                        self.logger.info(f"✅ Found password field with selector: {selector_type} = {selector_value}")
                        break
                    except:
                        continue

                if not password_field:
                    # Try to take a screenshot for debugging
                    try:
                        current_url = driver.current_url
                        page_source_snippet = driver.page_source[:500]
                        self.logger.error(f"❌ Could not find password field")
                        self.logger.error(f"Current URL: {current_url}")
                        self.logger.debug(f"Page source snippet: {page_source_snippet}")
                    except:
                        pass
                    return {'success': False, 'error': 'Password field not found after username entry'}

                # Human-like typing for password
                for char in password:
                    password_field.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))

                time.sleep(random.uniform(1, 2))

                # Click login button with multiple selector attempts
                self.logger.info("🔐 Submitting login...")
                login_button = None
                login_selectors = [
                    (By.CSS_SELECTOR, "div[data-testid='LoginForm_Login_Button']"),
                    (By.CSS_SELECTOR, "button[data-testid='LoginForm_Login_Button']"),
                    (By.XPATH, "//div[@data-testid='LoginForm_Login_Button']"),
                    (By.XPATH, "//button[contains(text(), 'Log in')]"),
                    (By.CSS_SELECTOR, "div[role='button']")
                ]

                for selector_type, selector_value in login_selectors:
                    try:
                        login_button = driver.find_element(selector_type, selector_value)
                        self.logger.info(f"✅ Found login button with selector: {selector_type} = {selector_value}")
                        break
                    except:
                        continue

                if not login_button:
                    self.logger.error("❌ Could not find login button")
                    return {'success': False, 'error': 'Login button not found'}

                login_button.click()
                
                # Wait for login result
                time.sleep(5)
                current_url = driver.current_url
                self.logger.info(f"Current URL after login: {current_url}")
                
                # Check for success indicators
                if self._check_login_success(driver):
                    self.logger.info("✅ Twitter login successful!")
                    
                    # Get session data
                    cookies = driver.get_cookies()
                    session_data = {
                        'username': username,
                        'authenticated': True,
                        'session_id': f'twitter_session_{username}_{datetime.now().timestamp()}',
                        'cookies': cookies,
                        'user_agent': driver.execute_script("return navigator.userAgent;"),
                        'current_url': current_url,
                        'authenticated_at': datetime.now().isoformat(),
                        'expires_at': (datetime.now() + timedelta(hours=24)).isoformat()
                    }
                    
                    return {
                        'success': True,
                        'message': f'Twitter authentication successful for @{username}',
                        'session_data': session_data
                    }
                else:
                    error_msg = self._get_error_message(driver)
                    self.logger.error(f"❌ Twitter login failed: {error_msg}")
                    return {
                        'success': False,
                        'error': error_msg or 'Login failed - unknown reason'
                    }
                    
            finally:
                if driver:
                    driver.quit()

        except Exception as e:
            self.logger.error(f"Twitter authentication failed: {str(e)}")
            return {
                'success': False,
                'error': f'Authentication failed: {str(e)}'
            }
    
    def _check_login_success(self, driver) -> bool:
        """Check if Twitter login was successful"""
        try:
            current_url = driver.current_url
            success_indicators = [
                "twitter.com/home",
                "x.com/home",
                "twitter.com/i/timeline",
                not "login" in current_url.lower()
            ]
            
            return any(success_indicators)
            
        except Exception as e:
            self.logger.error(f"Error checking login success: {str(e)}")
            return False
    
    def _get_error_message(self, driver) -> str:
        """Extract error message from Twitter login page"""
        try:
            error_selectors = [
                "[data-testid='error-message']",
                ".error-message",
                "[role='alert']",
                ".form-error"
            ]
            
            for selector in error_selectors:
                try:
                    error_element = driver.find_element(By.CSS_SELECTOR, selector)
                    if error_element.is_displayed():
                        return error_element.text
                except NoSuchElementException:
                    continue
            
            return "Login failed - no specific error message found"
            
        except Exception as e:
            self.logger.error(f"Error getting error message: {str(e)}")
            return "Login failed - error checking failed"

    def create_authenticated_scraper(self, username: str, password: str, email: str = None) -> 'TwitterScraper':
        """
        Create an authenticated TwitterScraper instance.

        Args:
            username: Twitter username
            password: Twitter password
            email: Twitter email (optional)

        Returns:
            Configured TwitterScraper instance
        """
        try:
            from ..scrapers.twitter_scraper import TwitterScraper

            # Create scraper with authentication credentials
            scraper = TwitterScraper(
                email=email,
                username=username,
                password=password,
                max_retries=3,
                delay_between_scrolls=3,
                rate_limit_delay=2
            )

            self.logger.info(f"Created authenticated scraper for @{username}")
            return scraper

        except Exception as e:
            self.logger.error(f"Failed to create authenticated scraper: {str(e)}")
            raise e

    def verify_credentials(self, username: str, password: str, email: str = None) -> Dict[str, Any]:
        """
        Verify Twitter credentials without full authentication.

        Args:
            username: Twitter username
            password: Twitter password
            email: Twitter email (optional)

        Returns:
            Verification result dictionary
        """
        try:
            self.logger.info(f"Verifying credentials for @{username}")

            # Use the authenticate method but don't save session
            result = self.authenticate(username, password, email)

            if result.get('success'):
                return {
                    'success': True,
                    'message': f'Credentials verified for @{username}',
                    'username': username
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Credential verification failed')
                }

        except Exception as e:
            self.logger.error(f"Credential verification failed: {str(e)}")
            return {
                'success': False,
                'error': f'Verification failed: {str(e)}'
            }
