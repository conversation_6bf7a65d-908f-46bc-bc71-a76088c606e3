"""
TikTok Persistent Session Scraper using Selenium
Uses non-incognito mode with user data directory for session persistence
"""
import os
import time
import json
import logging
from typing import Dict, List, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class TikTokPersistentSelenium:
    """
    TikTok scraper using Selenium with persistent session (non-incognito mode).
    Maintains login state across browser restarts using user data directory.
    """
    
    def __init__(self, user_data_dir: str = None):
        self.driver = None
        self.is_logged_in = False
        self.current_username = None
        self.user_data_dir = user_data_dir or os.path.join(os.getcwd(), 'tiktok_chrome_profile')
        
        # Ensure user data directory exists
        os.makedirs(self.user_data_dir, exist_ok=True)
        
    def _setup_driver(self):
        """Setup Chrome driver with persistent session (non-incognito mode)"""
        if self.driver:
            return
        
        try:
            options = Options()
            
            # CRITICAL: Use user data directory for session persistence
            options.add_argument(f"--user-data-dir={self.user_data_dir}")
            options.add_argument("--profile-directory=TikTokProfile")
            
            # DO NOT use incognito mode - we want persistent sessions
            # options.add_argument("--incognito")  # ❌ REMOVED
            
            # Anti-detection settings
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            options.add_argument("--disable-features=VizDisplayCompositor")
            
            # Realistic browser settings
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--start-maximized")
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Disable automation indicators
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Create driver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            
            # Anti-detection scripts
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """)
            
            logger.info(f"✅ Selenium driver initialized with persistent profile: {self.user_data_dir}")
            
        except Exception as e:
            logger.error(f"Failed to setup Selenium driver: {e}")
            raise
    
    def check_existing_session(self) -> bool:
        """Check if there's already a valid TikTok session"""
        try:
            if not self.driver:
                self._setup_driver()
            
            logger.info("🔍 Checking for existing TikTok session...")
            
            # Navigate to TikTok homepage
            self.driver.get("https://www.tiktok.com")
            time.sleep(3)
            
            # Check for login indicators
            login_indicators = [
                "//div[@data-e2e='profile-icon']",
                "//a[contains(@href, '/profile')]",
                "//div[@data-e2e='nav-profile']",
                "//button[@data-e2e='top-profile-avatar']"
            ]
            
            for indicator in login_indicators:
                try:
                    element = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, indicator))
                    )
                    if element:
                        logger.info(f"✅ Existing session found via: {indicator}")
                        self.is_logged_in = True
                        return True
                except TimeoutException:
                    continue
            
            logger.info("❌ No existing session found")
            return False
            
        except Exception as e:
            logger.error(f"Error checking existing session: {e}")
            return False
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """
        Login to TikTok with session persistence.
        First checks for existing session, only logs in if needed.
        """
        try:
            if not self.driver:
                self._setup_driver()
            
            # First check if already logged in
            if self.check_existing_session():
                logger.info(f"♻️ Using existing TikTok session (no login needed)")
                self.current_username = username
                return {
                    'success': True,
                    'message': 'Using existing session',
                    'session_reused': True
                }
            
            logger.info(f"🔐 Starting fresh TikTok login for @{username}")
            
            # Navigate to login page
            self.driver.get("https://www.tiktok.com/login/phone-or-email/email")
            time.sleep(3)
            
            # Fill username
            username_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_input.clear()
            username_input.send_keys(username)
            time.sleep(1)
            
            # Fill password
            password_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='password']"))
            )
            password_input.clear()
            password_input.send_keys(password)
            time.sleep(1)
            
            # Click login button
            login_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']"))
            )
            login_button.click()
            
            # Wait for login to complete
            logger.info("⏳ Waiting for login to complete...")
            
            # Check for successful login
            success_indicators = [
                "//div[@data-e2e='profile-icon']",
                "//a[contains(@href, '/profile')]",
                "//div[@data-e2e='nav-profile']"
            ]
            
            login_success = False
            for indicator in success_indicators:
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.XPATH, indicator))
                    )
                    login_success = True
                    logger.info(f"✅ Login success detected via: {indicator}")
                    break
                except TimeoutException:
                    continue
            
            if login_success:
                self.is_logged_in = True
                self.current_username = username
                
                # Session is automatically saved to user data directory
                logger.info(f"✅ TikTok login successful for @{username} - session saved to profile")
                return {
                    'success': True,
                    'message': f'Login successful for @{username}',
                    'session_reused': False
                }
            else:
                logger.error(f"❌ TikTok login failed for @{username}")
                return {
                    'success': False,
                    'error': 'Login failed - success indicators not found'
                }
                
        except Exception as e:
            logger.error(f"❌ TikTok login error for @{username}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def search_videos(self, query: str, limit: int = 20) -> Dict[str, Any]:
        """Search for videos on TikTok using persistent session"""
        try:
            if not self.is_logged_in:
                return {'success': False, 'error': 'Not logged in'}
            
            logger.info(f"🔍 Searching TikTok for: '{query}' (limit: {limit})")
            
            # Navigate to search page
            search_url = f"https://www.tiktok.com/search/video?q={query.replace(' ', '%20')}"
            self.driver.get(search_url)
            time.sleep(3)
            
            # Wait for search results
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-e2e*='search']"))
                )
            except TimeoutException:
                logger.warning("Search results took too long to load")
            
            videos = []
            video_selectors = [
                "div[data-e2e='search_top-item']",
                "div[data-e2e='search-card-item']",
                "[data-e2e='search-card-video']"
            ]
            
            # Find video elements
            video_elements = []
            for selector in video_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        video_elements = elements
                        logger.info(f"Found {len(elements)} video elements using: {selector}")
                        break
                except Exception:
                    continue
            
            # Extract video data
            for i, element in enumerate(video_elements[:limit]):
                try:
                    video_data = self._extract_video_data(element)
                    if video_data:
                        videos.append(video_data)
                        logger.info(f"Extracted video {i+1}: {video_data.get('description', 'N/A')[:50]}...")
                except Exception as e:
                    logger.warning(f"Failed to extract video {i+1}: {e}")
                    continue
            
            logger.info(f"✅ Successfully scraped {len(videos)} videos for query: '{query}'")
            
            return {
                'success': True,
                'videos': videos,
                'query': query,
                'total_found': len(videos),
                'source': 'selenium_persistent_session'
            }
            
        except Exception as e:
            logger.error(f"❌ TikTok search failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
    
    def get_user_videos(self, username: str, limit: int = 20) -> Dict[str, Any]:
        """Get videos from a specific user using persistent session"""
        try:
            if not self.is_logged_in:
                return {'success': False, 'error': 'Not logged in'}
            
            logger.info(f"👤 Getting videos from @{username} (limit: {limit})")
            
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            self.driver.get(profile_url)
            time.sleep(3)
            
            videos = []
            video_selectors = [
                "[data-e2e='user-post-item']",
                ".video-feed-item",
                "[data-e2e='user-post-item-video']"
            ]
            
            # Find video elements
            video_elements = []
            for selector in video_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        video_elements = elements
                        break
                except Exception:
                    continue
            
            # Extract video data
            for i, element in enumerate(video_elements[:limit]):
                try:
                    video_data = self._extract_video_data(element)
                    if video_data:
                        video_data['author_username'] = username
                        videos.append(video_data)
                except Exception as e:
                    logger.warning(f"Failed to extract user video {i+1}: {e}")
                    continue
            
            logger.info(f"✅ Successfully scraped {len(videos)} videos from @{username}")
            
            return {
                'success': True,
                'videos': videos,
                'username': username,
                'total_found': len(videos),
                'source': 'selenium_persistent_session'
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get user videos for @{username}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'username': username
            }
    
    def _extract_video_data(self, element) -> Optional[Dict[str, Any]]:
        """Extract video data from a video element"""
        try:
            video_data = {
                'source': 'selenium_persistent_session',
                'scraped_at': time.time()
            }
            
            # Extract description
            desc_selectors = [
                "[data-e2e='search-card-desc']",
                ".video-meta-title",
                "[data-e2e='video-desc']"
            ]
            
            for selector in desc_selectors:
                try:
                    desc_element = element.find_element(By.CSS_SELECTOR, selector)
                    if desc_element:
                        description = desc_element.text.strip()
                        if description:
                            video_data['description'] = description
                            break
                except:
                    continue
            
            # Extract author
            author_selectors = [
                "[data-e2e='search-card-user-unique-id']",
                ".author-uniqueId",
                "[data-e2e='video-author-uniqueid']"
            ]
            
            for selector in author_selectors:
                try:
                    author_element = element.find_element(By.CSS_SELECTOR, selector)
                    if author_element:
                        author = author_element.text.strip().replace('@', '')
                        if author:
                            video_data['author_username'] = author
                            break
                except:
                    continue
            
            # Extract URL
            try:
                link_element = element.find_element(By.CSS_SELECTOR, "a[href*='/video/']")
                if link_element:
                    href = link_element.get_attribute('href')
                    if href:
                        video_data['url'] = href
            except:
                pass
            
            # Add defaults
            video_data.setdefault('description', 'TikTok Video')
            video_data.setdefault('author_username', 'unknown')
            video_data.setdefault('likes', 0)
            video_data.setdefault('comments', 0)
            video_data.setdefault('shares', 0)
            video_data.setdefault('views', 0)
            
            return video_data
            
        except Exception as e:
            logger.warning(f"Failed to extract video data: {e}")
            return None
    
    def close(self):
        """Close the browser but keep session data"""
        try:
            if self.driver:
                # Session data is automatically saved to user data directory
                logger.info("🔒 Closing browser (session data preserved in profile)")
                self.driver.quit()
                self.driver = None
                # Don't reset login state - session persists
                # self.is_logged_in = False  # Keep this True for next startup
                
        except Exception as e:
            logger.error(f"Error closing Selenium driver: {e}")
    
    def restart_with_session(self) -> bool:
        """Restart browser and check if session is still valid"""
        try:
            logger.info("🔄 Restarting browser with persistent session...")
            
            # Close current driver if exists
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            # Setup new driver (will use same user data directory)
            self._setup_driver()
            
            # Check if session is still valid
            return self.check_existing_session()
            
        except Exception as e:
            logger.error(f"Error restarting with session: {e}")
            return False
