"""
Real TikTok Scraper

Real TikTok scraper using Selenium WebDriver for authentic data extraction.
NO MOCK DATA - Only real scraping with login credentials.
"""

import logging
import time
import random
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class TikTokScraper:
    """
    Real TikTok scraper using Selenium WebDriver for authentic data extraction.
    NO MOCK DATA - Only real scraping with login credentials.
    """
    
    def __init__(self, actor_account=None):
        self.logger = logger
        self.driver = None
        self.base_url = "https://www.tiktok.com"
        self.login_url = "https://www.tiktok.com/login/phone-or-email/email"
        self.is_logged_in = False
        self.current_user = None
        self.actor_account = actor_account
    
    def _setup_driver(self):
        """Setup standard Chrome driver with anti-detection"""
        if self.driver:
            return

        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # Create driver with WebDriverManager
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    def _find_element_by_selectors(self, selectors, element_name, timeout=10):
        """Find element using priority list of XPath selectors"""
        for i, selector in enumerate(selectors, 1):
            try:
                self.logger.debug(f"Trying {element_name} selector {i}/{len(selectors)}: {selector}")
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                self.logger.info(f"✅ Found {element_name} using selector {i}: {selector}")
                return element
            except (TimeoutException, NoSuchElementException):
                self.logger.debug(f"❌ Selector {i} failed for {element_name}: {selector}")
                continue

        self.logger.error(f"❌ Could not find {element_name} with any selector")
        return None

    def _handle_popups(self):
        """Handle popups and cookie banners using priority selectors"""
        self.logger.info("🚫 Handling popups and cookie banners...")

        # Cookie banner selectors
        cookie_selectors = [
            "//button[contains(text(), 'Accept')]",
            "//button[contains(text(), 'accept')]",
            "//button[contains(text(), 'Allow')]",
            "//button[contains(text(), 'OK')]",
            "//button[@data-e2e='cookie-accept']",
            "//button[@data-e2e='cookie-banner-accept']"
        ]

        # Try to close cookie banners
        for selector in cookie_selectors:
            try:
                cookie_btn = WebDriverWait(self.driver, 2).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                cookie_btn.click()
                self.logger.info(f"✅ Closed cookie banner with: {selector}")
                time.sleep(1)
                break
            except (TimeoutException, NoSuchElementException):
                continue

        # Popup close selectors
        popup_selectors = [
            "//button[contains(@class, 'close')]",
            "//div[contains(@class, 'close')]",
            "//*[@data-e2e='modal-close']",
            "//*[@data-e2e='popup-close']",
            "//button[@aria-label='Close']"
        ]

        # Try to close popups
        for selector in popup_selectors:
            try:
                popup_btn = WebDriverWait(self.driver, 2).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                popup_btn.click()
                self.logger.info(f"✅ Closed popup with: {selector}")
                time.sleep(1)
                break
            except (TimeoutException, NoSuchElementException):
                continue
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """
        Login to TikTok with real credentials
        
        Args:
            username: TikTok username or email
            password: TikTok password
            
        Returns:
            Dict with login result
        """
        try:
            self.logger.info(f"🚀 Starting TikTok login for: {username}")
            
            self._setup_driver()
            
            # Navigate to login page
            self.logger.info("📱 Navigating to TikTok login page...")
            self.driver.get(self.login_url)
            time.sleep(random.uniform(3, 5))
            
            # Handle popups and cookies first
            self._handle_popups()

            # Wait for and fill email field using priority selectors
            self.logger.info("✍️ Filling login credentials...")
            email_selectors = [
                "//input[@autocomplete='username']",
                "//input[@name='username']",
                "//input[@type='email']",
                "//input[contains(@placeholder, 'email')]",
                "//form//input[1]"
            ]

            email_field = self._find_element_by_selectors(email_selectors, "email input")
            if not email_field:
                raise Exception("Could not find email input field")

            email_field.clear()
            # Human-like typing
            for char in username:
                email_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            time.sleep(random.uniform(1, 2))

            # Fill password field using priority selectors
            password_selectors = [
                "//input[@type='password']",
                "//input[@autocomplete='current-password']",
                "//input[@name='password']",
                "//form//input[@type='password']"
            ]

            password_field = self._find_element_by_selectors(password_selectors, "password input")
            if not password_field:
                # Save debug info if password field not found
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = os.path.join('debug', f'tiktok_login_error_{timestamp}.png')
                html_path = os.path.join('debug', f'tiktok_login_error_{timestamp}.html')
                self.driver.save_screenshot(screenshot_path)
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(self.driver.page_source)
                self.logger.error(f"Password field not found. Debug saved to {screenshot_path} and {html_path}")
                raise Exception("Could not find password input field")

            password_field.clear()
            for char in password:
                password_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            time.sleep(random.uniform(1, 2))
            
            # Click login button using priority selectors
            self.logger.info("🔐 Submitting login...")
            login_selectors = [
                "//button[@type='submit']",
                "//button[contains(text(), 'Log in')]",
                "//input[@type='submit']",
                "//form//button[last()]"
            ]

            login_button = self._find_element_by_selectors(login_selectors, "login button")
            if not login_button:
                raise Exception("Could not find login button")

            login_button.click()
            
            # Check for success indicators by waiting for a post-login element
            if self._check_login_success():
                self.logger.info("✅ Login successful!")
                self.is_logged_in = True
                self.current_user = username
                
                return {
                    'success': True,
                    'message': 'Login successful',
                    'username': username
                }
            else:
                error_msg = self._get_error_message()
                self.logger.warning(f"❌ Login failed: {error_msg}")
                
                return {
                    'success': False,
                    'error': error_msg or 'Login failed - unknown reason'
                }
                
        except (TimeoutException, NoSuchElementException) as e:
            self.logger.error(f"❌ Login failed with exception: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            self.logger.error(f"❌ An unexpected error occurred during login: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def search_videos(self, query: str, count: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Search for TikTok videos using real scraping with Selenium.
        NO MOCK DATA - Only real scraping.
        """
        try:
            if not self.is_logged_in:
                return {
                    'success': False,
                    'error': 'Must login first before searching videos'
                }
            
            self.logger.info(f"🔍 Searching TikTok for: {query}")
            
            # Navigate to search page
            search_url = f"https://www.tiktok.com/search?q={query}&t=videos"
            self.driver.get(search_url)
            time.sleep(random.uniform(3, 5))
            
            # Wait for videos to load
            self.logger.info("⏳ Waiting for videos to load...")
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[@data-e2e='search-video-item']"))
            )
            
            # Scroll to load more videos
            videos_data = []
            scroll_attempts = 0
            max_scrolls = min(count // 10, 5)  # Limit scrolling
            
            while len(videos_data) < count and scroll_attempts < max_scrolls:
                # Extract current videos
                video_elements = self.driver.find_elements(By.XPATH, "//*[@data-e2e='search-video-item']")
                
                for element in video_elements:
                    if len(videos_data) >= count:
                        break
                    
                    try:
                        video_data = self._extract_video_data(element)
                        if video_data and video_data not in videos_data:
                            videos_data.append(video_data)
                    except Exception as e:
                        self.logger.warning(f"Failed to extract video data: {str(e)}")
                        continue
                
                # Scroll down to load more
                if len(videos_data) < count:
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(random.uniform(2, 4))
                    scroll_attempts += 1
            
            self.logger.info(f"✅ Successfully scraped {len(videos_data)} real TikTok videos")
            
            return {
                'success': True,
                'videos': videos_data[:count],
                'query': query,
                'count': len(videos_data[:count]),
                'timestamp': datetime.now().isoformat(),
                'source': 'real_tiktok_scraper',
                'logged_in_user': self.current_user
            }
            
        except Exception as e:
            self.logger.error(f"❌ Search failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
    
    def scrape_feed(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Scrape the user's feed/timeline."""
        if not self.is_logged_in:
            raise Exception("Must be logged in to scrape feed.")

        self.logger.info(f"Scraping feed for user {self.current_user}")
        self.driver.get(self.base_url)
        time.sleep(random.uniform(3, 5))

        videos_data = []
        scroll_attempts = 0
        max_scrolls = (limit // 5) + 1

        while len(videos_data) < limit and scroll_attempts < max_scrolls:
            self.logger.info(f"Scrolling to load more videos from feed... (Attempt {scroll_attempts + 1})")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(random.uniform(3, 5))

            video_elements = self.driver.find_elements(By.XPATH, "//*[@data-e2e='recommend-list-item-container']")
            self.logger.info(f"Found {len(video_elements)} video elements on the feed.")

            for element in video_elements:
                if len(videos_data) >= limit:
                    break
                try:
                    video_data = self._extract_video_data_from_feed(element)
                    if video_data and video_data['id'] not in [v['id'] for v in videos_data]:
                        videos_data.append(video_data)
                        self.logger.info(f"Scraped video from feed: {video_data['id']}")
                except Exception as e:
                    self.logger.warning(f"Could not extract video data from feed: {e}")

            scroll_attempts += 1

        self.logger.info(f"Scraped {len(videos_data)} videos from the feed.")
        return videos_data[:limit]

    def scrape_user_videos(self, username: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Scrape a specific user's videos."""
        if not self.is_logged_in:
            raise Exception("Must be logged in to scrape user videos.")

        self.logger.info(f"Scraping videos for user {username}")
        self.driver.get(f"{self.base_url}/@{username}")
        time.sleep(random.uniform(3, 5))

        videos_data = []
        scroll_attempts = 0
        max_scrolls = (limit // 12) + 1

        while len(videos_data) < limit and scroll_attempts < max_scrolls:
            self.logger.info(f"Scrolling to load more videos... (Attempt {scroll_attempts + 1})")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(random.uniform(2, 4))

            video_elements = self.driver.find_elements(By.XPATH, "//*[@data-e2e='user-post-item']")
            self.logger.info(f"Found {len(video_elements)} video elements on the page.")

            for element in video_elements:
                if len(videos_data) >= limit:
                    break
                try:
                    video_data = self._extract_video_data(element)
                    if video_data and video_data['id'] not in [v['id'] for v in videos_data]:
                        videos_data.append(video_data)
                        self.logger.info(f"Scraped video: {video_data['id']}")
                except Exception as e:
                    self.logger.warning(f"Could not extract video data: {e}")

            scroll_attempts += 1

        self.logger.info(f"Scraped {len(videos_data)} videos for user {username}.")
        return videos_data[:limit]

    def search_videos_by_hashtag(self, hashtag: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for videos by hashtag."""
        if not self.is_logged_in:
            raise Exception("Must be logged in to search by hashtag.")

        self.logger.info(f"Searching for videos with hashtag #{hashtag}")
        self.driver.get(f"{self.base_url}/tag/{hashtag.lstrip('#')}")
        time.sleep(random.uniform(3, 5))

        videos_data = []
        scroll_attempts = 0
        max_scrolls = (limit // 12) + 1

        while len(videos_data) < limit and scroll_attempts < max_scrolls:
            self.logger.info(f"Scrolling to load more videos for hashtag #{hashtag}... (Attempt {scroll_attempts + 1})")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(random.uniform(2, 4))

            video_elements = self.driver.find_elements(By.XPATH, "//*[@data-e2e='challenge-video-item']")
            self.logger.info(f"Found {len(video_elements)} video elements for hashtag #{hashtag}.")

            for element in video_elements:
                if len(videos_data) >= limit:
                    break
                try:
                    video_data = self._extract_video_data(element)
                    if video_data and video_data['id'] not in [v['id'] for v in videos_data]:
                        videos_data.append(video_data)
                        self.logger.info(f"Scraped video for hashtag #{hashtag}: {video_data['id']}")
                except Exception as e:
                    self.logger.warning(f"Could not extract video data for hashtag: {e}")

            scroll_attempts += 1

        self.logger.info(f"Scraped {len(videos_data)} videos for hashtag #{hashtag}.")
        return videos_data[:limit]

    def close(self):
        """Close the WebDriver session."""
        if self.driver:
            self.logger.info("Closing WebDriver session.")
            self.driver.quit()
            self.driver = None

    def _extract_video_data_from_feed(self, element) -> Optional[Dict[str, Any]]:
        """Extracts video data from a feed video element."""
        try:
            video_id_element = element.find_element(By.XPATH, ".//*[@data-e2e='recommend-video-card-container']")
            video_id = video_id_element.get_attribute('id').split('-')[-1]
            author_element = element.find_element(By.XPATH, ".//*[@data-e2e='video-author-name']")
            author_name = author_element.text
            video_url = f"https://www.tiktok.com/@{author_name}/video/{video_id}"

            desc_element = element.find_element(By.XPATH, ".//*[@data-e2e='video-desc']")
            description = desc_element.text

            return {
                'id': video_id,
                'url': video_url,
                'author': author_name,
                'description': description,
                'scraped_at': datetime.now().isoformat()
            }
        except NoSuchElementException:
            self.logger.warning("Could not find all elements for a feed video, skipping.")
            return None
        except Exception as e:
            self.logger.error(f"Error extracting feed video data: {e}")
            return None

    def _extract_video_data(self, element) -> Optional[Dict[str, Any]]:
        """Extracts video data from a Selenium element."""
        try:
            video_link_element = element.find_element(By.XPATH, ".//a")
            video_url = video_link_element.get_attribute('href')
            video_id = video_url.split("/")[-1]

            desc_element = element.find_element(By.XPATH, ".//*[@data-e2e='video-desc']")
            description = desc_element.text

            return {
                'id': video_id,
                'url': video_url,
                'description': description,
                'scraped_at': datetime.now().isoformat()
            }
        except NoSuchElementException:
            self.logger.warning("Could not find all elements for a video, skipping.")
            return None
        except Exception as e:
            self.logger.error(f"Error extracting video data: {e}")
            return None

    def _check_login_success(self) -> bool:
        """Check if login was successful by waiting for a post-login element."""
        try:
            # Wait for an element that only appears after a successful login.
            # The user's avatar in the header is a good candidate.
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[@data-e2e='header-avatar']"))
            )
            self.logger.info("Login success confirmed by presence of header avatar.")
            return True
        except TimeoutException:
            self.logger.warning("Login success check failed: Header avatar not found after 10s.")
            return False
        except Exception as e:
            self.logger.error(f"Error checking login success: {str(e)}")
            return False
    
    def _get_error_message(self) -> str:
        """Extract error message from login page"""
        try:
            # Give the page a moment to display an error
            time.sleep(1)

            
            # Convert CSS selectors to XPath
            error_xpath_selectors = [
                "//*[@data-e2e='login-error-message']",  # A more specific potential selector
                "//*[contains(@class, 'error-message')]",
                "//*[@data-e2e='error-message']",
                "//*[contains(@class, 'login-error')]",
                "//*[contains(@class, 'form-error')]",
                "//*[contains(@class, 'captcha_verify_container')]",  # Check for CAPTCHA
            ]

            for selector in error_xpath_selectors:
                try:
                    error_element = self.driver.find_element(By.XPATH, selector)
                    if error_element.is_displayed():
                        if "captcha" in selector:
                            return "Login failed: CAPTCHA challenge detected."
                        return error_element.text
                except NoSuchElementException:
                    continue
            
            return "Login failed - no specific error message found"
            
        except Exception as e:
            self.logger.error(f"Error getting error message: {str(e)}")
            return "Login failed - error checking failed"
    
    def _extract_video_data(self, element) -> Dict[str, Any]:
        """Extract real video data from TikTok video element"""
        try:
            # Extract video link
            video_link = element.find_element(By.XPATH, ".//a").get_attribute("href")

            # Extract video description
            try:
                desc_element = element.find_element(By.XPATH, ".//*[@data-e2e='browse-video-desc']")
                description = desc_element.text
            except NoSuchElementException:
                description = ""

            # Extract user info
            try:
                user_element = element.find_element(By.XPATH, ".//*[@data-e2e='user-title']")
                username = user_element.text
            except NoSuchElementException:
                username = "unknown"

            # Extract engagement metrics
            try:
                like_element = element.find_element(By.XPATH, ".//*[@data-e2e='like-count']")
                like_count = like_element.text
            except NoSuchElementException:
                like_count = "0"

            try:
                comment_element = element.find_element(By.XPATH, ".//*[@data-e2e='comment-count']")
                comment_count = comment_element.text
            except NoSuchElementException:
                comment_count = "0"

            try:
                share_element = element.find_element(By.XPATH, ".//*[@data-e2e='share-count']")
                share_count = share_element.text
            except NoSuchElementException:
                share_count = "0"
            
            return {
                'video_id': video_link.split('/')[-1] if video_link else '',
                'video_url': video_link,
                'description': description,
                'username': username,
                'like_count': like_count,
                'comment_count': comment_count,
                'share_count': share_count,
                'scraped_at': datetime.now().isoformat(),
                'real_data': True
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting video data: {str(e)}")
            return None

    def get_user_videos(self, username: str, count: int = 10, **kwargs) -> Dict[str, Any]:
        """
        Get videos from a specific TikTok user using real scraping.
        NO MOCK DATA - Only real scraping.
        """
        try:
            if not self.is_logged_in:
                return {
                    'success': False,
                    'error': 'Must login first before getting user videos'
                }

            self.logger.info(f"👤 Getting videos from @{username}")

            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            self.driver.get(profile_url)
            time.sleep(random.uniform(3, 5))

            # Wait for videos to load
            self.logger.info("⏳ Waiting for user videos to load...")
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[@data-e2e='user-post-item']"))
            )

            # Scroll to load more videos
            videos_data = []
            scroll_attempts = 0
            max_scrolls = min(count // 10, 5)  # Limit scrolling

            while len(videos_data) < count and scroll_attempts < max_scrolls:
                # Extract current videos
                video_elements = self.driver.find_elements(By.XPATH, "//*[@data-e2e='user-post-item']")

                for element in video_elements:
                    if len(videos_data) >= count:
                        break

                    try:
                        video_data = self._extract_video_data(element)
                        if video_data and video_data not in videos_data:
                            videos_data.append(video_data)
                    except Exception as e:
                        self.logger.warning(f"Failed to extract video data: {str(e)}")
                        continue

                # Scroll down to load more
                if len(videos_data) < count:
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(random.uniform(2, 4))
                    scroll_attempts += 1

            self.logger.info(f"✅ Successfully scraped {len(videos_data)} videos from @{username}")

            return {
                'success': True,
                'videos': videos_data[:count],
                'username': username,
                'count': len(videos_data[:count]),
                'timestamp': datetime.now().isoformat(),
                'source': 'real_tiktok_scraper',
                'logged_in_user': self.current_user
            }

        except Exception as e:
            self.logger.error(f"❌ User videos scraping failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'username': username
            }

    def close(self):
        """Close the browser driver"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                self.is_logged_in = False
                self.current_user = None
                self.logger.info("🔒 Browser driver closed")
            except Exception as e:
                self.logger.error(f"Error closing driver: {str(e)}")

    def __del__(self):
        """Cleanup when object is destroyed"""
        self.close()


# Create alias for backward compatibility
class RealTikTokScraper(TikTokScraper):
    """Alias for TikTokScraper to maintain backward compatibility"""
    pass
