"""
TikTok Playwright Scraper - Alternative implementation using <PERSON><PERSON>
for better login success and bot detection avoidance.
"""
import os
import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

logger = logging.getLogger(__name__)

class TikTokPlaywrightScraper:
    """
    TikTok scraper using <PERSON><PERSON> for better login success and bot detection avoidance.
    This implementation focuses on real data scraping with persistent sessions.
    """
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.is_logged_in = False
        self.current_username = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self, user_data_dir: str = None):
        """Initialize Playwright browser with persistent session"""
        try:
            self.playwright = await async_playwright().start()

            # Set user data directory for persistent sessions
            if not user_data_dir:
                user_data_dir = os.path.join(os.getcwd(), 'tiktok_playwright_profile')

            os.makedirs(user_data_dir, exist_ok=True)

            # Launch browser with persistent context (non-incognito)
            self.browser = await self.playwright.chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                headless=False,  # Set to True for production
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images',  # Faster loading
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ],
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )

            # Use the persistent context directly (no separate context needed)
            self.context = self.browser
            
            # Create page
            self.page = await self.context.new_page()
            
            # Add stealth scripts
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            logger.info("✅ Playwright TikTok scraper initialized with persistent session")

        except Exception as e:
            logger.error(f"Failed to initialize Playwright scraper: {e}")
            raise

    async def check_existing_session(self) -> bool:
        """Check if there's already a valid TikTok session"""
        try:
            if not self.page:
                await self.initialize()

            logger.info("🔍 Checking for existing TikTok session...")

            # Navigate to TikTok homepage
            await self.page.goto('https://www.tiktok.com', wait_until='networkidle', timeout=30000)
            await asyncio.sleep(2)

            # Check for login indicators
            success_indicators = [
                'div[data-e2e="profile-icon"]',
                'a[href*="/profile"]',
                'div[data-e2e="nav-profile"]',
                '[data-e2e="top-profile-avatar"]'
            ]

            for indicator in success_indicators:
                try:
                    await self.page.wait_for_selector(indicator, timeout=5000)
                    logger.info(f"✅ Existing session found via: {indicator}")
                    self.is_logged_in = True
                    return True
                except:
                    continue

            logger.info("❌ No existing session found")
            return False

        except Exception as e:
            logger.error(f"Error checking existing session: {e}")
            return False
    
    async def login(self, username: str, password: str) -> Dict[str, Any]:
        """
        Login to TikTok using Playwright with enhanced bot detection avoidance.
        
        Args:
            username: TikTok username
            password: TikTok password
            
        Returns:
            Dict with success status and session info
        """
        try:
            if not self.page:
                await self.initialize()

            # First check if already logged in
            if await self.check_existing_session():
                logger.info(f"♻️ Using existing TikTok session (no login needed)")
                self.current_username = username
                return {
                    'success': True,
                    'message': 'Using existing session',
                    'session_reused': True
                }

            logger.info(f"🔐 Starting fresh TikTok login for @{username} using Playwright")
            
            # Navigate to TikTok login page
            await self.page.goto('https://www.tiktok.com/login/phone-or-email/email', 
                                wait_until='networkidle', timeout=30000)
            
            # Wait for page to load
            await asyncio.sleep(2)
            
            # Fill username/email
            email_input = await self.page.wait_for_selector('input[name="username"]', timeout=10000)
            await email_input.fill(username)
            await asyncio.sleep(1)
            
            # Fill password
            password_input = await self.page.wait_for_selector('input[type="password"]', timeout=10000)
            await password_input.fill(password)
            await asyncio.sleep(1)
            
            # Click login button
            login_button = await self.page.wait_for_selector('button[type="submit"]', timeout=10000)
            await login_button.click()
            
            # Wait for login to complete
            logger.info("⏳ Waiting for login to complete...")
            
            # Check for successful login (multiple possible indicators)
            success_indicators = [
                'div[data-e2e="profile-icon"]',  # Profile icon
                'a[href*="/profile"]',           # Profile link
                'div[data-e2e="nav-profile"]',   # Navigation profile
                '[data-e2e="top-profile-avatar"]' # Top profile avatar
            ]
            
            login_success = False
            for indicator in success_indicators:
                try:
                    await self.page.wait_for_selector(indicator, timeout=15000)
                    login_success = True
                    logger.info(f"✅ Login success detected via: {indicator}")
                    break
                except:
                    continue
            
            if login_success:
                self.is_logged_in = True
                self.current_username = username
                
                # Get session cookies for persistence
                cookies = await self.context.cookies()
                session_data = {
                    'cookies': cookies,
                    'username': username,
                    'login_time': time.time()
                }
                
                logger.info(f"✅ TikTok login successful for @{username}")
                return {
                    'success': True,
                    'message': f'Login successful for @{username}',
                    'session_data': session_data
                }
            else:
                # Check for error messages
                error_selectors = [
                    '[data-e2e="login-error"]',
                    '.error-message',
                    '[role="alert"]'
                ]
                
                error_message = "Login failed - unknown error"
                for selector in error_selectors:
                    try:
                        error_element = await self.page.wait_for_selector(selector, timeout=2000)
                        error_text = await error_element.text_content()
                        if error_text:
                            error_message = error_text
                            break
                    except:
                        continue
                
                logger.error(f"❌ TikTok login failed for @{username}: {error_message}")
                return {
                    'success': False,
                    'error': error_message
                }
                
        except Exception as e:
            logger.error(f"❌ TikTok login error for @{username}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def search_videos(self, query: str, limit: int = 20) -> Dict[str, Any]:
        """
        Search for videos on TikTok using Playwright.
        
        Args:
            query: Search query
            limit: Maximum number of videos to scrape
            
        Returns:
            Dict with success status and video data
        """
        try:
            if not self.is_logged_in:
                return {'success': False, 'error': 'Not logged in'}
            
            logger.info(f"🔍 Searching TikTok for: '{query}' (limit: {limit})")
            
            # Navigate to search page
            search_url = f"https://www.tiktok.com/search/video?q={query.replace(' ', '%20')}"
            await self.page.goto(search_url, wait_until='networkidle', timeout=30000)
            
            # Wait for search results to load
            await asyncio.sleep(3)
            
            videos = []
            video_selectors = [
                'div[data-e2e="search_top-item"]',
                'div[data-e2e="search-card-item"]',
                '[data-e2e="search-card-video"]'
            ]
            
            # Try different selectors for video containers
            video_elements = None
            for selector in video_selectors:
                try:
                    video_elements = await self.page.query_selector_all(selector)
                    if video_elements:
                        logger.info(f"Found video elements using selector: {selector}")
                        break
                except:
                    continue
            
            if not video_elements:
                logger.warning("No video elements found")
                return {'success': True, 'videos': [], 'query': query}
            
            # Extract video data
            for i, element in enumerate(video_elements[:limit]):
                try:
                    video_data = await self._extract_video_data(element)
                    if video_data:
                        videos.append(video_data)
                        logger.info(f"Extracted video {i+1}: {video_data.get('description', 'N/A')[:50]}...")
                except Exception as e:
                    logger.warning(f"Failed to extract video {i+1}: {e}")
                    continue
            
            logger.info(f"✅ Successfully scraped {len(videos)} videos for query: '{query}'")
            
            return {
                'success': True,
                'videos': videos,
                'query': query,
                'total_found': len(videos),
                'source': 'real_tiktok_playwright'
            }
            
        except Exception as e:
            logger.error(f"❌ TikTok search failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
    
    async def _extract_video_data(self, element) -> Optional[Dict[str, Any]]:
        """Extract video data from a video element"""
        try:
            # Extract basic video information
            video_data = {
                'source': 'real_tiktok_playwright',
                'scraped_at': time.time()
            }
            
            # Try to extract description/title
            desc_selectors = [
                '[data-e2e="search-card-desc"]',
                '.video-meta-title',
                '[data-e2e="video-desc"]'
            ]
            
            for selector in desc_selectors:
                try:
                    desc_element = await element.query_selector(selector)
                    if desc_element:
                        description = await desc_element.text_content()
                        if description:
                            video_data['description'] = description.strip()
                            break
                except:
                    continue
            
            # Try to extract author information
            author_selectors = [
                '[data-e2e="search-card-user-unique-id"]',
                '.author-uniqueId',
                '[data-e2e="video-author-uniqueid"]'
            ]
            
            for selector in author_selectors:
                try:
                    author_element = await element.query_selector(selector)
                    if author_element:
                        author = await author_element.text_content()
                        if author:
                            video_data['author_username'] = author.strip().replace('@', '')
                            break
                except:
                    continue
            
            # Try to extract video URL
            try:
                link_element = await element.query_selector('a[href*="/video/"]')
                if link_element:
                    href = await link_element.get_attribute('href')
                    if href:
                        video_data['url'] = f"https://www.tiktok.com{href}" if href.startswith('/') else href
            except:
                pass
            
            # Add default values if not found
            video_data.setdefault('description', 'TikTok Video')
            video_data.setdefault('author_username', 'unknown')
            video_data.setdefault('likes', 0)
            video_data.setdefault('comments', 0)
            video_data.setdefault('shares', 0)
            video_data.setdefault('views', 0)
            
            return video_data
            
        except Exception as e:
            logger.warning(f"Failed to extract video data: {e}")
            return None
    
    async def get_user_videos(self, username: str, limit: int = 20) -> Dict[str, Any]:
        """
        Get videos from a specific user's profile.
        
        Args:
            username: TikTok username
            limit: Maximum number of videos to scrape
            
        Returns:
            Dict with success status and video data
        """
        try:
            if not self.is_logged_in:
                return {'success': False, 'error': 'Not logged in'}
            
            logger.info(f"👤 Getting videos from @{username} (limit: {limit})")
            
            # Navigate to user profile
            profile_url = f"https://www.tiktok.com/@{username}"
            await self.page.goto(profile_url, wait_until='networkidle', timeout=30000)
            
            # Wait for profile to load
            await asyncio.sleep(3)
            
            # Extract user videos using similar logic to search
            videos = []
            video_selectors = [
                '[data-e2e="user-post-item"]',
                '.video-feed-item',
                '[data-e2e="user-post-item-video"]'
            ]
            
            video_elements = None
            for selector in video_selectors:
                try:
                    video_elements = await self.page.query_selector_all(selector)
                    if video_elements:
                        break
                except:
                    continue
            
            if video_elements:
                for i, element in enumerate(video_elements[:limit]):
                    try:
                        video_data = await self._extract_video_data(element)
                        if video_data:
                            video_data['author_username'] = username
                            videos.append(video_data)
                    except Exception as e:
                        logger.warning(f"Failed to extract user video {i+1}: {e}")
                        continue
            
            logger.info(f"✅ Successfully scraped {len(videos)} videos from @{username}")
            
            return {
                'success': True,
                'videos': videos,
                'username': username,
                'total_found': len(videos),
                'source': 'real_tiktok_playwright'
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get user videos for @{username}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'username': username
            }
    
    async def close(self):
        """Close the browser and cleanup resources"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            
            self.is_logged_in = False
            self.current_username = None
            logger.info("🔒 Playwright TikTok scraper closed")
            
        except Exception as e:
            logger.error(f"Error closing Playwright scraper: {e}")

# Synchronous wrapper for compatibility
class TikTokPlaywrightScraperSync:
    """Synchronous wrapper for the async Playwright scraper"""
    
    def __init__(self):
        self.async_scraper = TikTokPlaywrightScraper()
        self.loop = None
    
    def _run_async(self, coro):
        """Run async function in sync context"""
        try:
            # Try to get existing event loop
            self.loop = asyncio.get_event_loop()
        except RuntimeError:
            # Create new event loop if none exists
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
        
        return self.loop.run_until_complete(coro)
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Synchronous login wrapper"""
        async def _login():
            await self.async_scraper.initialize()
            return await self.async_scraper.login(username, password)
        
        return self._run_async(_login())
    
    def search_videos(self, query: str, limit: int = 20) -> Dict[str, Any]:
        """Synchronous search wrapper"""
        return self._run_async(self.async_scraper.search_videos(query, limit))
    
    def get_user_videos(self, username: str, limit: int = 20) -> Dict[str, Any]:
        """Synchronous user videos wrapper"""
        return self._run_async(self.async_scraper.get_user_videos(username, limit))
    
    def close(self):
        """Synchronous close wrapper"""
        self._run_async(self.async_scraper.close())
