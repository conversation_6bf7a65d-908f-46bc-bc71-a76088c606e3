import logging
import json
import time
import random
from datetime import datetime, timedelta
from celery import shared_task
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import transaction
from .models import TikTokUserAccount, ActorTask, ActorScrapedData, TikTokSession
from .utils.tiktok_auth import TikTokAuthenticator
from .utils.anti_detection import AntiDetectionManager
from .scrapers.tiktok_scraper import TikTok<PERSON>craper
from .utils.data_processor import process_actor_data
from .utils.session_manager import SessionManager
from .utils.proxy_manager import ProxyManager
from .services.actor_service import ActorService

__all__ = [
    'actor_scrape_task',  # Single unified task for all scraping
]

logger = logging.getLogger(__name__)

# ============================================================================
# UNIFIED ACTOR SCRAPING TASK
# ============================================================================

@shared_task(bind=True, max_retries=3)
def actor_scrape_task(self, task_id):
    """
    Unified Actor Scraping Task

    This single task handles all types of scraping (Twitter and TikTok) based on the task configuration.
    Supports:
    - Twitter content search, user scraping, feed scraping
    - TikTok user videos, hashtag scraping, targeted user scraping
    - Automatic platform detection and appropriate scraper selection
    """
    try:
        from .models import ActorTask, ActorScrapedData

        # Get the task
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.progress = 0
        task.save()

        logger.info(f"Starting unified scraping task {task_id}: {task.task_type} on {task.platform}")

        # Update progress
        task.progress = 10
        task.status_message = "Initializing scraper..."
        task.save()

        # Route to appropriate scraper based on platform
        if task.platform.lower() == 'twitter':
            result = self._handle_twitter_scraping(task)
        elif task.platform.lower() == 'tiktok':
            result = self._handle_tiktok_scraping(task)
        else:
            raise ValueError(f"Unsupported platform: {task.platform}")

        # Update final status
        if result.get('success', False):
            task.status = 'COMPLETED'
            task.completed_at = timezone.now()
            task.progress = 100
            task.status_message = f"Completed - scraped {result.get('items_count', 0)} items"
            task.items_scraped = result.get('items_count', 0)
        else:
            task.status = 'FAILED'
            task.completed_at = timezone.now()
            task.error_message = result.get('error', 'Unknown error')
            task.status_message = f"Failed: {result.get('error', 'Unknown error')}"

        task.save()

        logger.info(f"Unified scraping task {task_id} completed with status: {task.status}")
        return result

    except Exception as e:
        logger.error(f"Unified scraping task {task_id} failed: {str(e)}")

        # Update task status to failed
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.progress = 0
            task.status_message = f"Failed: {str(e)}"
            task.save()
        except:
            pass

        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying unified scraping task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60)

        return {
            'success': False,
            'error': f'Unified scraping task failed: {str(e)}'
        }

def _handle_twitter_scraping(task):
    """Handle Twitter scraping based on task type."""
    try:
        from .scrapers.twitter_scraper import TwitterScraper

            # Get account credentials
        account = task.actor_account
        username = account.platform_username
        password = account.get_decrypted_password()
        email = account.email

        if not password:
            raise Exception("Account password not available for Twitter scraping")

        # Update progress
        task.progress = 20
        task.status_message = f"Setting up Twitter scraper for @{username}..."
        task.save()

        # Initialize Twitter scraper
        scraper = TwitterScraper(
            email=email,
            username=username,
                password=password,
                max_retries=3,
                delay_between_scrolls=3,
                rate_limit_delay=2
            )

            # Update progress
            task.progress = 30
            task.status_message = "Starting Twitter scraping..."
            task.save()

            # Determine scraping parameters based on task type
            if task.task_type == 'CONTENT_SEARCH':
                # Content search
                query = task.keywords or "twitter"
                result = scraper.search_tweets(
                    query=query,
                    count=task.max_items or 10,
                    start_date=task.start_date.strftime('%Y-%m-%d') if task.start_date else None,
                    end_date=task.end_date.strftime('%Y-%m-%d') if task.end_date else None
                )
            elif task.task_type == 'USER_SCRAPE':
                # User scraping
                target_user = task.target_username or username
                result = scraper.search_tweets(
                    query=f"from:{target_user}",
                    count=task.max_items or 10
                )
            elif task.task_type == 'FEED_SCRAPE':
                # Feed scraping
                result = scraper.search_tweets(
                    query="trending news Indonesia",
                    count=task.max_items or 10
                )
            else:
                raise ValueError(f"Unsupported Twitter task type: {task.task_type}")

            # Close scraper
            scraper.close()

            # Update progress
            task.progress = 70
            task.status_message = "Processing scraped data..."
            task.save()

            # Process results
            if result.get('success'):
                tweets = result.get('tweets', [])
                items_saved = self._save_twitter_data(task, tweets)

                return {
                    'success': True,
                    'items_count': items_saved,
                    'platform': 'twitter'
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Twitter scraping failed')
                }

        except Exception as e:
            logger.error(f"Twitter scraping failed: {str(e)}")
            return {
                'success': False,
                'error': f'Twitter scraping failed: {str(e)}'
            }

    def _handle_tiktok_scraping(self, task):
        """Handle TikTok scraping based on task type."""
        try:
            from .scrapers.tiktok_scraper import TikTokScraper

            # Get account
            account = task.actor_account

            # Update progress
            task.progress = 20
            task.status_message = f"Setting up TikTok scraper for @{account.platform_username}..."
            task.save()

            # Initialize TikTok scraper
            scraper = TikTokScraper()

            # Update progress
            task.progress = 30
            task.status_message = "Starting TikTok scraping..."
            task.save()

            # Determine scraping parameters based on task type
            if task.task_type == 'USER_SCRAPE':
                # User videos scraping
                target_user = task.target_username or account.platform_username
                result = scraper.scrape_user_videos(target_user, limit=task.max_items or 10)
            elif task.task_type == 'HASHTAG_SCRAPE':
                # Hashtag scraping
                hashtag = task.keywords or "trending"
                result = scraper.scrape_hashtag_videos(hashtag, limit=task.max_items or 10)
            elif task.task_type == 'CONTENT_SEARCH':
                # General content search
                query = task.keywords or "trending"
                result = scraper.search_videos(query, limit=task.max_items or 10)
            else:
                raise ValueError(f"Unsupported TikTok task type: {task.task_type}")

            # Close scraper
            scraper.close()

            # Update progress
            task.progress = 70
            task.status_message = "Processing scraped data..."
            task.save()

            # Process results
            if result.get('success'):
                videos = result.get('videos', [])
                items_saved = self._save_tiktok_data(task, videos)

                return {
                    'success': True,
                    'items_count': items_saved,
                    'platform': 'tiktok'
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'TikTok scraping failed')
                }

        except Exception as e:
            logger.error(f"TikTok scraping failed: {str(e)}")
            return {
                'success': False,
                'error': f'TikTok scraping failed: {str(e)}'
            }

    def _save_twitter_data(self, task, tweets):
        """Save Twitter data to database."""
        items_saved = 0
        total_tweets = len(tweets)

        for i, tweet in enumerate(tweets):
            try:
                # Extract tweet ID from enhanced scraper format
                tweet_id = tweet.get('id', f"twitter_{items_saved + 1}")
                tweet_text = tweet.get('text', 'N/A')

                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='twitter',
                    data_type='TWEET',
                    content=tweet,
                    actor_account=task.actor_account,
                    account_username=task.actor_account.platform_username,
                    platform_content_id=tweet_id,
                    is_complete=True,
                    quality_score=1.0  # Enhanced scraper provides real data
                )
                items_saved += 1

                # Update progress during saving
                if i % 5 == 0 or i == total_tweets - 1:
                    progress = 70 + (25 * (i + 1) / total_tweets)
                    task.progress = int(progress)
                    task.status_message = f"Saved {items_saved}/{total_tweets} tweets..."
                    task.save()

                logger.debug(f"Saved Twitter item {items_saved}: {tweet_text[:50]}...")

            except Exception as e:
                logger.error(f"Failed to save Twitter item: {str(e)}")
                continue

        return items_saved

    def _save_tiktok_data(self, task, videos):
        """Save TikTok data to database."""
        items_saved = 0
        total_videos = len(videos)

        for i, video in enumerate(videos):
            try:
                # Extract video ID from TikTok scraper format
                video_id = video.get('id', f"tiktok_{items_saved + 1}")
                video_desc = video.get('description', 'N/A')

                scraped_data = ActorScrapedData.objects.create(
                    task=task,
                    platform='tiktok',
                    data_type='VIDEO',
                    content=video,
                    actor_account=task.actor_account,
                    account_username=task.actor_account.platform_username,
                    platform_content_id=video_id,
                    is_complete=True,
                    quality_score=1.0  # Real scraper provides real data
                )
                items_saved += 1

                # Update progress during saving
                if i % 5 == 0 or i == total_videos - 1:
                    progress = 70 + (25 * (i + 1) / total_videos)
                    task.progress = int(progress)
                    task.status_message = f"Saved {items_saved}/{total_videos} videos..."
                    task.save()

                logger.debug(f"Saved TikTok item {items_saved}: {video_desc[:50]}...")

            except Exception as e:
                logger.error(f"Failed to save TikTok item: {str(e)}")
                continue

        return items_saved

    except Exception as e:
        logger.error(f"Unified scraping task {task_id} failed: {str(e)}")

        # Update task status to failed
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.progress = 0
            task.status_message = f"Failed: {str(e)}"
            task.save()
        except:
            pass

        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying unified scraping task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60)

        return {
            'success': False,
            'error': f'Unified scraping task failed: {str(e)}'
        }


