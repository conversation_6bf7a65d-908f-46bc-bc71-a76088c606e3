from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import threading
import time
from typing import Dict, Optional
from django.utils import timezone
from .models import ActorAccount
import logging

logger = logging.getLogger(__name__)

class WebDriverSessionManager:
    """Manages persistent WebDriver sessions per account"""
    
    def __init__(self):
        self._sessions: Dict[str, webdriver.Chrome] = {}
        self._session_lock = threading.Lock()
        self._cleanup_thread = None
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """Start background thread to cleanup inactive sessions"""
        if self._cleanup_thread is None or not self._cleanup_thread.is_alive():
            self._cleanup_thread = threading.Thread(target=self._cleanup_inactive_sessions, daemon=True)
            self._cleanup_thread.start()
    
    def _cleanup_inactive_sessions(self):
        """Background task to cleanup inactive webdriver sessions"""
        while True:
            try:
                time.sleep(300)  # Check every 5 minutes
                with self._session_lock:
                    inactive_keys = []
                    for session_key, driver in self._sessions.items():
                        try:
                            # Check if driver is still responsive
                            driver.current_url
                            
                            # Check if account is still marked as active
                            account_id = session_key.split('_')[-1]
                            account = ActorAccount.objects.filter(id=account_id, webdriver_active=True).first()
                            
                            if not account:
                                inactive_keys.append(session_key)
                            elif account.last_used and (timezone.now() - account.last_used).total_seconds() > 3600:  # 1 hour
                                inactive_keys.append(session_key)
                                
                        except (WebDriverException, Exception):
                            inactive_keys.append(session_key)
                    
                    # Cleanup inactive sessions
                    for key in inactive_keys:
                        self._close_session(key)
                        
            except Exception as e:
                logger.error(f"Error in cleanup thread: {e}")
                time.sleep(60)  # Wait before retrying
    
    def _create_webdriver(self) -> webdriver.Chrome:
        """Create a new Chrome WebDriver instance with anti-detection settings"""
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # Create driver with WebDriverManager
        try:
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
        except ImportError:
            # Fallback to standard driver creation
            driver = webdriver.Chrome(options=options)

        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver
    
    def get_or_create_session(self, account: ActorAccount) -> webdriver.Chrome:
        """Get existing session or create new one for account"""
        session_key = account.get_webdriver_session_key()
        
        with self._session_lock:
            # Check if session already exists and is valid
            if session_key in self._sessions:
                driver = self._sessions[session_key]
                try:
                    # Test if driver is still responsive
                    driver.current_url
                    account.mark_webdriver_active()
                    return driver
                except (WebDriverException, Exception):
                    # Driver is dead, remove it
                    self._close_session(session_key)
            
            # Create new session
            try:
                driver = self._create_webdriver()
                self._sessions[session_key] = driver
                account.mark_webdriver_active()
                logger.info(f"Created new WebDriver session for account {account.platform_username}")
                return driver
            except Exception as e:
                logger.error(f"Failed to create WebDriver session for account {account.platform_username}: {e}")
                raise
    
    def _close_session(self, session_key: str):
        """Close and remove a session"""
        if session_key in self._sessions:
            try:
                self._sessions[session_key].quit()
            except Exception as e:
                logger.warning(f"Error closing WebDriver session {session_key}: {e}")
            finally:
                del self._sessions[session_key]
                
                # Update account status
                try:
                    account_id = session_key.split('_')[-1]
                    account = ActorAccount.objects.filter(id=account_id).first()
                    if account:
                        account.mark_webdriver_inactive()
                except Exception as e:
                    logger.warning(f"Error updating account status for session {session_key}: {e}")
    
    def close_session(self, account: ActorAccount):
        """Close session for specific account"""
        session_key = account.get_webdriver_session_key()
        with self._session_lock:
            self._close_session(session_key)
    
    def close_all_sessions(self):
        """Close all active sessions"""
        with self._session_lock:
            session_keys = list(self._sessions.keys())
            for session_key in session_keys:
                self._close_session(session_key)
    
    def get_session_count(self) -> int:
        """Get number of active sessions"""
        with self._session_lock:
            return len(self._sessions)
    
    def login_to_platform(self, account: ActorAccount, driver: webdriver.Chrome) -> bool:
        """Login to the platform using account credentials"""
        try:
            if account.platform.lower() == 'tiktok':
                return self._login_to_tiktok(account, driver)
            elif account.platform.lower() == 'twitter':
                return self._login_to_twitter(account, driver)
            # Add other platforms as needed
            else:
                logger.warning(f"Platform {account.platform} not supported for auto-login")
                return False
        except Exception as e:
            logger.error(f"Login failed for {account.platform_username}: {e}")
            return False
    
    def _login_to_tiktok(self, account: ActorAccount, driver: webdriver.Chrome) -> bool:
        """Login to TikTok"""
        try:
            driver.get('https://www.tiktok.com/login/phone-or-email/email')
            time.sleep(3)
            
            # Enter email/username
            email_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            email_input.clear()
            email_input.send_keys(account.email or account.platform_username)
            
            # Enter password
            password_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_input.clear()
            password_input.send_keys(account.get_decrypted_password())
            
            # Click login button
            login_button = driver.find_element(By.CSS_SELECTOR, "button[data-e2e='login-button']")
            login_button.click()
            
            # Wait for login to complete
            WebDriverWait(driver, 15).until(
                lambda d: 'login' not in d.current_url.lower()
            )
            
            logger.info(f"Successfully logged into TikTok for {account.platform_username}")
            return True
            
        except TimeoutException:
            logger.error(f"Login timeout for TikTok account {account.platform_username}")
            return False
        except Exception as e:
            logger.error(f"TikTok login error for {account.platform_username}: {e}")
            return False
    
    def _login_to_twitter(self, account: ActorAccount, driver: webdriver.Chrome) -> bool:
        """Login to Twitter/X"""
        try:
            driver.get('https://twitter.com/i/flow/login')
            time.sleep(3)
            
            # Enter username/email
            username_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[autocomplete='username']"))
            )
            username_input.clear()
            username_input.send_keys(account.email or account.platform_username)
            
            # Click next
            next_button = driver.find_element(By.XPATH, "//span[text()='Next']")
            next_button.click()
            time.sleep(2)
            
            # Enter password
            password_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[name='password']"))
            )
            password_input.clear()
            password_input.send_keys(account.get_decrypted_password())
            
            # Click login
            login_button = driver.find_element(By.XPATH, "//span[text()='Log in']")
            login_button.click()
            
            # Wait for login to complete
            WebDriverWait(driver, 15).until(
                lambda d: 'home' in d.current_url.lower() or 'twitter.com' in d.current_url
            )
            
            logger.info(f"Successfully logged into Twitter for {account.platform_username}")
            return True
            
        except TimeoutException:
            logger.error(f"Login timeout for Twitter account {account.platform_username}")
            return False
        except Exception as e:
            logger.error(f"Twitter login error for {account.platform_username}: {e}")
            return False

# Global instance
webdriver_manager = WebDriverSessionManager()