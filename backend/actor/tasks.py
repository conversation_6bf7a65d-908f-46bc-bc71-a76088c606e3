import logging
import json
import time
import random
from datetime import datetime, timedelta
from celery import shared_task
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import transaction
from .models import TikTokUserAccount, ActorTask, ActorScrapedData, TikTokSession
from .utils.tiktok_auth import TikTokAuthenticator
from .utils.anti_detection import AntiDetectionManager
from .scrapers.tiktok_scraper import TikTok<PERSON>craper
from .utils.data_processor import process_actor_data
from .utils.session_manager import SessionManager
from .utils.proxy_manager import ProxyManager
from .services.actor_service import ActorService

__all__ = [
    'actor_scrape_task',  # Single unified task for all scraping
]

logger = logging.getLogger(__name__)

# ============================================================================
# UNIFIED ACTOR SCRAPING TASK
# ============================================================================

def log_task_message(task, level, message, details=None):
    """Helper function to log task messages to database"""
    try:
        from .models import ActorTaskLog
        ActorTaskLog.objects.create(
            task=task,
            level=level.upper(),
            message=message,
            details=details,
            source='celery_task'
        )
        # Also log to console
        getattr(logger, level.lower())(f"Task {task.id}: {message}")
    except Exception as e:
        logger.error(f"Failed to log task message: {e}")

@shared_task(bind=True, max_retries=3)
def actor_scrape_task(self, task_id):
    """
    Unified Actor Scraping Task

    This single task handles all types of scraping (Twitter and TikTok) based on the task configuration.
    Supports:
    - Twitter content search, user scraping, feed scraping
    - TikTok user videos, hashtag scraping, targeted user scraping
    - Automatic platform detection and appropriate scraper selection
    """
    try:
        from .models import ActorTask, ActorScrapedData

        # Get the task
        task = ActorTask.objects.get(id=task_id)
        task.status = 'RUNNING'
        task.started_at = timezone.now()
        task.progress = 0
        task.save()

        log_task_message(task, 'info', f"Starting unified scraping task {task_id}: {task.task_type} on {task.platform}")
        log_task_message(task, 'info', f"Task parameters: Keywords={getattr(task, 'keywords', 'N/A')}, Max items={getattr(task, 'max_items', 'N/A')}")

        # Update progress
        task.progress = 10
        task.save()
        log_task_message(task, 'info', "Task initialized - setting up scraper...")
        
        # Route to appropriate scraper based on platform
        log_task_message(task, 'info', f"Routing to {task.platform} scraper...")

        if task.platform.lower() == 'twitter':
            result = _handle_twitter_scraping(task)
        elif task.platform.lower() == 'tiktok':
            result = _handle_tiktok_scraping(task)
        else:
            error_msg = f"Unsupported platform: {task.platform}"
            log_task_message(task, 'error', error_msg)
            raise ValueError(error_msg)

        # Update final status
        if result.get('success', False):
            task.status = 'COMPLETED'
            task.completed_at = timezone.now()
            task.progress = 100
            task.items_scraped = result.get('items_count', 0)
            log_task_message(task, 'success', f"Task completed successfully! Scraped {task.items_scraped} items")
        else:
            task.status = 'FAILED'
            task.completed_at = timezone.now()
            task.error_message = result.get('error', 'Unknown error')
            log_task_message(task, 'error', f"Task failed: {task.error_message}")

        task.save()

        log_task_message(task, 'info', f"Unified scraping task {task_id} completed with status: {task.status}")
        return result

    except Exception as e:
        logger.error(f"Unified scraping task {task_id} failed: {str(e)}")
        
        # Update task status to failed
        try:
            task = ActorTask.objects.get(id=task_id)
            task.status = 'FAILED'
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.progress = 0
            task.save()
        except:
            pass
        
        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying unified scraping task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=e, countdown=60)
        
        return {
            'success': False,
            'error': f'Unified scraping task failed: {str(e)}'
        }

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def _handle_twitter_scraping(task):
    """Handle Twitter scraping based on task type."""
    try:
        from .scrapers.twitter_scraper import TwitterScraper

        log_task_message(task, 'info', "Initializing Twitter scraper...")

        # Get account credentials
        account = task.actor_account
        username = account.platform_username
        password = account.get_decrypted_password()
        email = account.email

        if not password:
            error_msg = "Account password not available for Twitter scraping"
            log_task_message(task, 'error', error_msg)
            raise Exception(error_msg)

        log_task_message(task, 'info', f"Starting Twitter scraping with account @{username}")

        # Update progress
        task.progress = 20
        task.save()

        # Initialize Twitter scraper
        log_task_message(task, 'info', "Setting up Twitter scraper with authentication...")
        scraper = TwitterScraper(
            email=email,
            username=username,
            password=password,
            max_retries=3,
            delay_between_scrolls=3,
            rate_limit_delay=2
        )

        log_task_message(task, 'info', f"Executing Twitter task type: {task.task_type}")

        # Update progress
        task.progress = 30
        task.save()
        
        # Determine scraping parameters based on task type
        if task.task_type == 'CONTENT_SEARCH':
            # Content search
            query = task.keywords or "twitter"
            result = scraper.search_tweets(
                query=query,
                count=task.max_items or 10,
                start_date=task.start_date.strftime('%Y-%m-%d') if task.start_date else None,
                end_date=task.end_date.strftime('%Y-%m-%d') if task.end_date else None
            )
        elif task.task_type == 'TARGETED_USER':
            # User scraping
            target_user = task.target_identifier or username
            result = scraper.search_tweets(
                query=f"from:{target_user}",
                count=task.max_items or 10
            )
        elif task.task_type == 'FEED_SCRAPE':
            # Feed scraping
            result = scraper.search_tweets(
                query="trending news Indonesia",
                count=task.max_items or 10
            )
        else:
            raise ValueError(f"Unsupported Twitter task type: {task.task_type}")
        
        # Close scraper
        scraper.close()
        
        # Update progress
        task.progress = 70
        task.save()
        
        # Process results
        if result.get('success'):
            tweets = result.get('tweets', [])
            items_saved = _save_twitter_data(task, tweets)
            
            return {
                'success': True,
                'items_count': items_saved,
                'platform': 'twitter'
            }
        else:
            return {
                'success': False,
                'error': result.get('error', 'Twitter scraping failed')
            }
            
    except Exception as e:
        logger.error(f"Twitter scraping failed: {str(e)}")
        return {
            'success': False,
            'error': f'Twitter scraping failed: {str(e)}'
        }

def _handle_tiktok_scraping(task):
    """Handle TikTok scraping based on task type."""
    try:
        from .scrapers.tiktok_scraper import TikTokScraper
        
        # Get account
        account = task.actor_account
        
        # Update progress
        task.progress = 20
        task.save()

        # Initialize TikTok scraper
        scraper = TikTokScraper()

        # Update progress
        task.progress = 30
        task.save()
        
        # Determine scraping parameters based on task type
        if task.task_type == 'TARGETED_USER':
            # User videos scraping
            target_user = task.target_identifier or account.platform_username
            result = scraper.scrape_user_videos(target_user, limit=task.max_items or 10)
        elif task.task_type == 'HASHTAG_ANALYSIS':
            # Hashtag scraping
            hashtag = task.keywords or "trending"
            result = scraper.scrape_hashtag_videos(hashtag, limit=task.max_items or 10)
        elif task.task_type == 'CONTENT_SEARCH':
            # General content search
            query = task.keywords or "trending"
            result = scraper.search_videos(query, limit=task.max_items or 10)
        else:
            raise ValueError(f"Unsupported TikTok task type: {task.task_type}")
        
        # Close scraper
        scraper.close()
        
        # Update progress
        task.progress = 70
        task.save()
        
        # Process results
        if result.get('success'):
            videos = result.get('videos', [])
            items_saved = _save_tiktok_data(task, videos)
            
            return {
                'success': True,
                'items_count': items_saved,
                'platform': 'tiktok'
            }
        else:
            return {
                'success': False,
                'error': result.get('error', 'TikTok scraping failed')
            }
            
    except Exception as e:
        logger.error(f"TikTok scraping failed: {str(e)}")
        return {
            'success': False,
            'error': f'TikTok scraping failed: {str(e)}'
        }

def _save_twitter_data(task, tweets):
    """Save Twitter data to database."""
    items_saved = 0
    total_tweets = len(tweets)
    
    for i, tweet in enumerate(tweets):
        try:
            # Extract tweet ID from enhanced scraper format
            tweet_id = tweet.get('id', f"twitter_{items_saved + 1}")
            tweet_text = tweet.get('text', 'N/A')
            
            scraped_data = ActorScrapedData.objects.create(
                task=task,
                platform='twitter',
                data_type='TWEET',
                content=tweet,
                actor_account=task.actor_account,
                account_username=task.actor_account.platform_username,
                platform_content_id=tweet_id,
                is_complete=True,
                quality_score=1.0  # Enhanced scraper provides real data
            )
            items_saved += 1
            
            # Update progress during saving
            if i % 5 == 0 or i == total_tweets - 1:
                progress = 70 + (25 * (i + 1) / total_tweets)
                task.progress = int(progress)
                task.save()
            
            logger.debug(f"Saved Twitter item {items_saved}: {tweet_text[:50]}...")
            
        except Exception as e:
            logger.error(f"Failed to save Twitter item: {str(e)}")
            continue
    
    return items_saved

def _save_tiktok_data(task, videos):
    """Save TikTok data to database."""
    items_saved = 0
    total_videos = len(videos)
    
    for i, video in enumerate(videos):
        try:
            # Extract video ID from TikTok scraper format
            video_id = video.get('id', f"tiktok_{items_saved + 1}")
            video_desc = video.get('description', 'N/A')
            
            scraped_data = ActorScrapedData.objects.create(
                task=task,
                platform='tiktok',
                data_type='VIDEO',
                content=video,
                actor_account=task.actor_account,
                account_username=task.actor_account.platform_username,
                platform_content_id=video_id,
                is_complete=True,
                quality_score=1.0  # Real scraper provides real data
            )
            items_saved += 1
            
            # Update progress during saving
            if i % 5 == 0 or i == total_videos - 1:
                progress = 70 + (25 * (i + 1) / total_videos)
                task.progress = int(progress)
                task.save()
            
            logger.debug(f"Saved TikTok item {items_saved}: {video_desc[:50]}...")
            
        except Exception as e:
            logger.error(f"Failed to save TikTok item: {str(e)}")
            continue
    
    return items_saved
