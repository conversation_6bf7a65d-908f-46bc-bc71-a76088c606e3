# Generated by Django 5.2.4 on 2025-07-29 20:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor', '0005_remove_actoraccount_last_authenticated_at_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='actoraccount',
            name='platform',
            field=models.CharField(choices=[('tiktok', 'TikTok'), ('twitter', 'Twitter')], help_text='Social media platform', max_length=20),
        ),
        migrations.AlterField(
            model_name='actorscrapeddata',
            name='platform',
            field=models.CharField(choices=[('tiktok', 'TikTok'), ('twitter', 'Twitter')], default='tiktok', help_text='Platform where data was scraped from', max_length=20),
        ),
        migrations.AlterField(
            model_name='actorsession',
            name='platform',
            field=models.CharField(choices=[('tiktok', 'TikTok'), ('twitter', 'Twitter')], help_text='Platform for this session', max_length=20),
        ),
        migrations.AlterField(
            model_name='actortask',
            name='platform',
            field=models.CharField(choices=[('tiktok', 'TikTok'), ('twitter', 'Twitter')], default='tiktok', help_text='Platform for this task', max_length=20),
        ),
        migrations.CreateModel(
            name='ActorTaskLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('level', models.CharField(choices=[('DEBUG', 'Debug'), ('INFO', 'Info'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('SUCCESS', 'Success')], default='INFO', max_length=10)),
                ('message', models.TextField(help_text='Log message')),
                ('details', models.JSONField(blank=True, help_text='Additional log details', null=True)),
                ('source', models.CharField(default='celery_task', help_text='Source of the log (celery_task, scraper, etc.)', max_length=50)),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='actor.actortask')),
            ],
            options={
                'verbose_name': 'Actor Task Log',
                'verbose_name_plural': 'Actor Task Logs',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['task', 'timestamp'], name='actor_actor_task_id_a3b086_idx'), models.Index(fields=['level'], name='actor_actor_level_6303cd_idx')],
            },
        ),
    ]
