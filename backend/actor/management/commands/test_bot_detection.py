from django.core.management.base import BaseCommand
from django.utils import timezone
from actor.models import TikTokUserAccount
from actor.utils.session_manager import EnhancedSessionManager
from actor.utils.tiktok_auth import TikTokAuthenticator
import logging
import json

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Test and monitor bot detection system'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--account-id',
            type=int,
            help='Specific account ID to test'
        )
        parser.add_argument(
            '--test-login',
            action='store_true',
            help='Test login with enhanced bot detection'
        )
        parser.add_argument(
            '--health-check',
            action='store_true',
            help='Check health of all accounts'
        )
        parser.add_argument(
            '--simulate-detection',
            action='store_true',
            help='Simulate bot detection scenarios'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
    
    def handle(self, *args, **options):
        if options['verbose']:
            logging.basicConfig(level=logging.DEBUG)
        
        session_manager = EnhancedSessionManager()
        
        if options['health_check']:
            self.check_account_health(session_manager)
        
        if options['test_login']:
            account_id = options.get('account_id')
            if account_id:
                self.test_login_for_account(account_id)
            else:
                self.stdout.write(self.style.ERROR('--account-id is required for login test'))
        
        if options['simulate_detection']:
            self.simulate_bot_detection_scenarios(session_manager)
    
    def check_account_health(self, session_manager):
        """Check health of all active accounts"""
        self.stdout.write(self.style.SUCCESS('\n=== Account Health Check ==='))
        
        accounts = TikTokUserAccount.objects.filter(is_active=True)
        
        if not accounts.exists():
            self.stdout.write(self.style.WARNING('No active accounts found'))
            return
        
        healthy_count = 0
        unhealthy_count = 0
        
        for account in accounts:
            try:
                health_score = session_manager.get_account_health_score(account.id)
                overall_score = health_score.get('overall_score', 0)
                risk_level = health_score.get('risk_level', 'unknown')
                
                status_color = self.style.SUCCESS if overall_score >= 0.6 else self.style.WARNING
                if overall_score < 0.3:
                    status_color = self.style.ERROR
                
                self.stdout.write(
                    status_color(
                        f"Account {account.id} ({account.username}): "
                        f"Score {overall_score:.2f}, Risk: {risk_level}"
                    )
                )
                
                # Show recommendations for unhealthy accounts
                if overall_score < 0.6:
                    recommendations = health_score.get('recommendations', [])
                    for rec in recommendations[:3]:  # Show top 3 recommendations
                        self.stdout.write(f"  → {rec}")
                    unhealthy_count += 1
                else:
                    healthy_count += 1
                
                # Show account status
                if account.is_blocked:
                    self.stdout.write(
                        self.style.ERROR(
                            f"  ⚠️  Account is blocked until {account.blocked_until}"
                        )
                    )
                
                if account.login_attempts > 0:
                    self.stdout.write(
                        self.style.WARNING(
                            f"  ⚠️  {account.login_attempts} failed login attempts"
                        )
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error checking account {account.id}: {str(e)}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"\nSummary: {healthy_count} healthy, {unhealthy_count} unhealthy accounts"
            )
        )
    
    def test_login_for_account(self, account_id):
        """Test login for a specific account"""
        self.stdout.write(self.style.SUCCESS(f'\n=== Testing Login for Account {account_id} ==='))
        
        try:
            account = TikTokUserAccount.objects.get(id=account_id)
            
            # Check if account is blocked
            if account.is_blocked and account.blocked_until > timezone.now():
                self.stdout.write(
                    self.style.ERROR(
                        f"Account is blocked until {account.blocked_until}"
                    )
                )
                return
            
            # Initialize authenticator
            authenticator = TikTokAuthenticator()
            
            self.stdout.write(f"Testing login for {account.username}...")
            
            # Attempt login with retry count
            result = authenticator.login(retry_count=1)
            
            if result.get('success'):
                self.stdout.write(
                    self.style.SUCCESS("✅ Login successful!")
                )
                
                # Show session quality
                session_quality = result.get('session_quality', 0)
                anti_bot_score = result.get('anti_bot_score', 0)
                
                self.stdout.write(
                    f"Session Quality: {session_quality:.2f}"
                )
                self.stdout.write(
                    f"Anti-Bot Score: {anti_bot_score:.2f}"
                )
                
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ Login failed: {result.get('error', 'Unknown error')}")
                )
                
                # Show failure reason
                failure_reason = result.get('failure_reason')
                if failure_reason:
                    self.stdout.write(f"Failure Reason: {failure_reason}")
                
                # Show bot detection indicators
                bot_detected = result.get('bot_detected', False)
                if bot_detected:
                    self.stdout.write(
                        self.style.ERROR("🤖 Bot detection triggered!")
                    )
                    
                    detection_indicators = result.get('detection_indicators', [])
                    for indicator in detection_indicators:
                        self.stdout.write(f"  → {indicator}")
        
        except TikTokUserAccount.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Account {account_id} not found")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error testing login: {str(e)}")
            )
    
    def simulate_bot_detection_scenarios(self, session_manager):
        """Simulate various bot detection scenarios"""
        self.stdout.write(self.style.SUCCESS('\n=== Bot Detection Simulation ==='))
        
        scenarios = [
            {
                'name': 'High Login Attempts',
                'description': 'Account with multiple failed login attempts',
                'simulate': self._simulate_high_login_attempts
            },
            {
                'name': 'Low Session Quality',
                'description': 'Account with poor session quality',
                'simulate': self._simulate_low_session_quality
            },
            {
                'name': 'Bot Detection Triggered',
                'description': 'Account that triggered bot detection',
                'simulate': self._simulate_bot_detection
            }
        ]
        
        for scenario in scenarios:
            self.stdout.write(f"\n--- {scenario['name']} ---")
            self.stdout.write(scenario['description'])
            
            try:
                scenario['simulate'](session_manager)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error in scenario: {str(e)}")
                )
    
    def _simulate_high_login_attempts(self, session_manager):
        """Simulate account with high login attempts"""
        # Find an account to simulate with
        account = TikTokUserAccount.objects.filter(is_active=True).first()
        if not account:
            self.stdout.write(self.style.WARNING("No active accounts to simulate with"))
            return
        
        # Temporarily increase login attempts
        original_attempts = account.login_attempts
        account.login_attempts = 4  # Close to the limit
        account.save()
        
        try:
            # Check if rotation is needed
            rotation_result = session_manager.rotate_session_if_needed(account.id)
            
            if rotation_result.get('should_rotate'):
                self.stdout.write(
                    self.style.WARNING(
                        f"✅ System correctly identified need for rotation: {rotation_result.get('reason')}"
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS("Account still considered healthy")
                )
            
            # Get health score
            health_score = session_manager.get_account_health_score(account.id)
            self.stdout.write(
                f"Health Score: {health_score.get('overall_score', 0):.2f}"
            )
            
        finally:
            # Restore original state
            account.login_attempts = original_attempts
            account.save()
    
    def _simulate_low_session_quality(self, session_manager):
        """Simulate account with low session quality"""
        self.stdout.write("Simulating low session quality scenario...")
        
        # This would typically involve creating a session with poor quality indicators
        # For now, we'll just show what the system would do
        self.stdout.write(
            "✅ System would apply cooldown and suggest session refresh"
        )
    
    def _simulate_bot_detection(self, session_manager):
        """Simulate bot detection scenario"""
        self.stdout.write("Simulating bot detection scenario...")
        
        # This would involve triggering the bot detection logic
        # For now, we'll show the expected response
        self.stdout.write(
            "✅ System would apply extended cooldown and rotate session"
        )
        self.stdout.write(
            "✅ System would recommend different browser fingerprint"
        )